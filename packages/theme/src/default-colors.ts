/**
 * Created by he<PERSON><PERSON> on 2020/3/11.
 */

export declare type Color = string;

export class Colors {
    blackMask10 = "rgba(0,0,0, 0.1)";
    blackMask60 = "rgba(0,0,0, 0.6)";
    red = "#f00";

    F6 = "#F6F9F9";

    transparent = "transparent";
    window_bg = "#F2F2F2";

    // 主题色
    theme1 = "#00a173";
    theme2 = "#08BB88";
    theme3 = "#2CCD9F";
    theme4 = "#E0F6EF";
    theme5 = "#CBFAED";
    theme6 = "#88EDD0";
    theme7 = "#3573CA";

    theme2Mask8 = "rgba(8, 187, 136, 0.08)";
    theme2Mask12 = "rgba(8, 187, 136, 0.12)";

    // 装饰色
    W1 = "#FFFFFF";
    W2 = "rgba(255, 255, 255, 0.75)";
    W3 = "rgba(255, 255, 255, 0.5)";
    W4 = "rgba(255, 255, 255, 0.25)";
    W5 = "rgba(255, 255, 255, 0.08)";

    B1 = "#007AFF";
    B2 = "#2680F7";
    B2Mask20 = "rgba(38, 128, 247, 0.2)";
    B3 = "#5199F8";
    B4 = "#E9F2FE";
    B5 = "#D4E6FD";
    B6 = "#85BAFF";
    B7 = "#0090FF";
    B8 = "#5D92D2";
    B9 = "#B0D4FF";
    B10 = "#E7F1FC";
    B11 = "#B7D5F6";

    S1 = "#000000";
    S2 = "#FFFFFF";
    S3 = "#2680F7";
    S4 = "#385068";
    S2Mask40 = "rgba(255,255,255,0.40)";
    s2Mask20 = "rgba(255,255,255,0.20)";
    s2Mask90 = "rgba(255,255,255,0.90)";

    // 文字色
    T0 = "#000000";
    T1 = "#333333";
    T1Mask20 = "rgba(51, 51, 51, 0.2)";
    T1Mask30 = "rgba(51, 51, 51, 0.3)";
    T2 = "#7A8794";
    T3 = "#96A4B3";
    T4 = "#CCCCCC";
    T5 = "#7A8795";
    T6 = "#AAABB3";
    T8 = "#3C95C4";
    T9 = "#626D77";

    // 界面色
    P1 = "#CED0DA";

    //  P2 :'#FFCED0DA';
    P3 = "#DADBE0";
    P4 = "#EFF3F6";
    P5 = "#E6EAEE";
    P6 = "#C1C9D0";
    P7 = "#EBF5FF";

    // 装饰色
    R1 = "#E52D5B";
    R2 = "#FF3333";
    R3 = "#FF5B84";
    R4 = "#FFEAEF";
    R5 = "#FFD6E0";
    R6 = "#F04A3E";

    Y1 = "#E5892D";
    Y2 = "#FF9933";
    Y2Mask8 = "rgba(255, 153, 51, 0.08)";
    Y3 = "#FFAD5B";
    Y4 = "#FFF4EA";
    Y5 = "#FFEBD6";
    Y6 = "#FF7433";
    Y7 = "#FFF7EF";

    G1 = "#08A446";
    G2 = "#0EBA52";
    G3 = "#23CF67";
    G4 = "#E3FCED";
    G5 = "#BBF2D1";
    //报表配色 -- chart 配色参照
    C1 = "#FD9800";
    C2 = "#0A8CEA";
    C3 = "#067CE0E";
    C4 = "#FF6464";
    C5 = "#FEC166";
    C6 = "#6CBAF2";

    D2 = "#F5F7FB";
    D3 = "#F5F9FB";

    //处方类型选择背景色
    Pc = "#F9F1E9";
    Pw = "#E7F3FF";
    Pi = "#DEFAF7";
    Pt = "#F5F7FB";

    // errorBorder = this.R2;
    errorBorder = "#FF9933";
    // errorBorderBg = "rgba(255,51,51,0.08)"; // 代码重构底部线条
    errorBorderBg = "rgba(255,153,51,0.08)"; // 代码重构底部线条
    errorLine = `rgba(255,153,51,0.2)`;

    bluePanelBg = "rgba(0,122,255,0.05)";
    blueLineColor = "rgba(38,128,247,0.20)";

    mainColor = "#08BB88";
    mainColorMask30 = "rgba(8,187,136,0.30)";
    mainColorMask60 = "rgba(8,187,136,0.60)";
    bottomBtnDisable = "#E6EAEE";
    lightMainColor = "#E0F6EF";

    mainTitleColor = "#000000";

    dividerLineColor = "#F0F0F0";

    contentBgColor = "white";
    maskBg = "#0000005c";
    cardYellowBackgroundColor = "#FFFDEC";

    priceColor = "#FF9933";
    popMenuBg = "#414244";

    maskColor = "#********";

    buttonPressed = "#D3D3DD";

    airPharmacyTextColor = "#C66E4E";

    //收费处退费"抵扣"标识文字颜色
    chargeRefundTagColor = "#979797";

    // abc宣传页面
    bankPropagandaBg = "#F8F8F8";
    bankTitleColor = "#333333";
    bankTipTitleColor = "#FF5100";

    //重构颜色

    mainColor_05 = `${this.mainColor}0D`;
    mainColor_08 = `${this.mainColor}14`;
    mainColor_20 = `${this.mainColor}33`;
    mainColor_008 = `${this.mainColor}08`;

    theme2_05 = `${this.theme2}0D`;
    theme2_08 = `${this.theme2}14`;
    theme2_10 = `${this.theme2}10`;

    //背景色
    bg1 = "#F8F8F8";
    bg2 = "#E28F4C10";
    bg3 = "#08BB8810";
    bg4 = "#ECECEC";
    bg5 = "#E28F4C15";

    toast_bg = "#333333F2"; // Toast背景色

    sheBaoTag_bg = "rgba(0, 0, 0, 0.03)"; // 社保标签背景色
    sheBaoCardOnTouch_bg = "#EEEEEE"; // 社保卡片点击状态态背景色

    whiteSmoke = "#F5F5F5"; // 白烟 245,245,245
    whiteSmoke_Mask08 = "rgba(245, 245, 245, 0.08)";

    bg_white = "#FFFFFF";
    bg_grey1 = "#F9FAFC";
    bg_grey2 = "#F2F4F7";
    bg_grey3 = "#F2F9FF";

    cp_white = "#FFFFFF";
    cp_black = "#484848";
    cp_grey1 = "#F9FAFC";
    cp_grey2 = "#F2F4F7";
    cp_grey3 = "#F2F9FF";
    cp_switch_normal = "#CED0DA";
    cp_switch_disabled = "#E6EAEE";
    cp_pressed = "#E6EAEE";
    cp_pressed_black = "rgba(0,0,0, 0.4)";
    cp_checked = "#F1F6FE";

    //文字色
    t1 = "#333333";
    t2 = "#777777";
    t3 = "#AAABB3";
    t4 = "#cccccc";
    t5 = "#FFFFFF";
    t6 = "rgba(255, 255, 255, 0.75)";
    t7 = "rgba(255, 255, 255, 0.5)";

    //边框颜色
    bdColor = "#DEDEDE"; // 置灰色（不可点击状态用色）

    panelBg = "#E7E7ED"; // 系统面板统一弹窗背景

    tagBg = "#ECFAF6";

    prescriptionBg = "#F2F2F7"; //处方背景色

    //重构颜色end
    //按钮点击态
    mainColorPress = "#06956C";
    //弹窗按钮点击态
    dialogBtnPress = "#F8F8F8";
    //键盘背景色
    keyboardBg = "#D6D7DC";

    femalePatientColor = "#FF6082"; // 女性患者图标颜色
    malePatientColor = "#58A0FF"; // 男性患者图标颜色
    freshGreen = "#08BB88"; // 清新绿

    selectTipColor = "#C6C6C6"; //选择提示文字颜色
    canAppointmentColor = "#1EC761"; //排班状态-可预约颜色
    abnormalColor = "#FFF7EF";
    priceStatistics = "rgba(248,248,248,0.30)";

    statTopBg = "#08BB88"; // 统计上方背景色
    pieChartColor = "#FFAD4A"; // 饼图颜色
    pieChartColor2 = "#FF3366"; // 饼图颜色2
    pieChartColor3 = "#722ED1"; // 饼图颜色3
    pieChartColor4 = "#40C6C2"; // 饼图颜色4
    pieChartColor5 = "#9FDB1D"; // 饼图颜色5
    pieChartColor6 = "#D91AD9"; // 饼图颜色6
    pieChartColor7 = "#FADC19"; // 饼图颜色7
    divingLineColor = "rgba(0,0,0,0.10)"; // 分割线颜色
    // 库存预警-商城价格更低
    lowPriceColor = "#E95247";
    lowPriceTextColor = "rgba(233, 82, 71, 0.70)";
    lowPriceBgColor = "rgba(233, 82, 71, 0.10)";
    lowPriceGoodsColor = "#333000";
    lowPriceMallBg = "rgba(233, 82, 71, 0.05)";

    // 蒙层色
    mask_heavy = "rgba(0,0,0, 0.8)";
    mask_normal = "rgba(0,0,0, 0.6)";
    mask_light = "rgba(0,0,0, 0.4)";

    // 线条颜色
    border_color_normal = "#CED0DA";
    border_color_light = "#E6E9ED";
    border_color_white1 = "#FFFFFF";
    border_color_white2 = "rgba(255, 255, 255, 0.25)";
    border_color_black1 = "rgba(0, 0, 0, 0.08)";
    border_color_theme1 = "#0090FF";
    border_color_theme2 = "#D2F2FF";
    border_color_success1 = "#1EC761";
    border_color_success2 = "#C5F1D6";
    border_color_warning1 = "#ED8218";
    border_color_warning2 = "#FBE3CB";
    border_color_error1 = "#D54941";
    border_color_error2 = "#F9D0D0";

    // 药店
    retail_bg_grey2 = "#F2F4F7";
    retail_theme5 = "#A2E3CD";
    retail_theme4 = "#E1F7F1";
    retail_R2 = "#D54941";
    retail_border_light = "#E6E9ED";
    retail_T3 = "#AAB4BF";
    retail_red3 = "#F9EBEB";
    retail_Y1 = "#ED8218";
    retail_yellow2 = "#FEF5ED";
    retail_theme_light = "#E8F5FF";
    retail_cp_grey = "#F9FAFC";
    retail_red_red3 = "#FCE4E4";
    retail_warning_light = "#FBE3CB";
    retail_cp_black = "#484848";
    retail_B2 = "#459EFF";
    retail_border_error2 = "#F9D0D0";
    retail_border_black = "rgba(0, 0, 0, 0.08)";
    retail_theme_topbar = "#3573CA";
    retail_cp_checked_grey = "#F1F6FE";
    retail_success_light = "#DDF7E7";
    retail_tag_border = "#C5F1D6";
    retail_B7_24 = "rgba(0,144,255,0.24)";
    G1Mask8 = "rgba(30, 199, 97, 0.08)";

    //     文本主题色
    black = "#000000";
    grey = "#7A8794";
    grey_light = "#AAB4BF";
    grey_pale = "#CED0DA";
    white = "#FFFFFF";
    white_light = "rgba(255,255,255,0.75)";
    white_pale = "rgba(255,255,255,0.5)";
    success = "#1EC761";
    theme = "#0090FF";
    warning = "#ED8218";
    error = "#D54941";

    // box-shadow
    shadow_keyboard = "rgba(0, 0, 0, 0.4)";
    shadow_ordinary = "rgba(0, 0, 0, 0.04)";
    shadow_heavy = "rgba(0, 0, 0, 0.12)";
    shadow_cpitem = "rgba(0, 0, 0, 0.08)";
}
