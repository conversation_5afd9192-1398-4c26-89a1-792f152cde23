import { Text, View } from "@hippy/react";
import { ABCStyles, Colors, Sizes, TextStyles } from "@app/theme";
import { AbcAssetImage } from "../abc-asset-image";
import { AbcSizedBox } from "../abc-size-box";
import { AbcRightArrowIcon } from "../abc-iconfont";
import React from "react";
import { AbcCountDown } from "../abc-count-down";
import { AbcView } from "../abc-view";

interface AbcBannerTips {
  iconName?: string;
  tips?: string;
  cancelText?: string;
  showHandleBtn?: boolean;
  duringTime?: number; // 倒计时时长
  lockFlag?: "english" | "chinese"; // 倒计时以中文形式还是英文形式
  cancelOperate?(): void; // 取消操作
  onClick?(): void; // 去处理
  countdownEndsCallback?(): void; // 倒计时结束回调
  onMaskClick?(): void; // 遮罩层点击回调
}
export const AbcBannerTips: React.FC<AbcBannerTips> = ({
    iconName,
    tips,
    cancelText,
    showHandleBtn = false,
    duringTime = 0,
    lockFlag,
    cancelOperate,
    onClick,
    countdownEndsCallback,
    onMaskClick,
}) => {
  return (
      <AbcView
          style={{
            ...ABCStyles.rowAlignCenterSpaceBetween,
            backgroundColor: Colors.abnormalColor,
            ...Sizes.paddingLTRB(Sizes.dp16, Sizes.dp10),
          }}
          onClick={() => onMaskClick?.()}
      >
        <View style={{ flexDirection: "row", flex: 1, flexWrap: "wrap", alignItems: "center" }}>
          <View style={{ alignSelf: "flex-start" }}>
            <AbcAssetImage
                name={iconName ?? "info-fill"}
                style={{
                  marginRight: Sizes.dp8,
                  width: Sizes.dp18,
                  height: Sizes.dp18,
                }}
            />
          </View>
          {!duringTime && (
              <View style={{ flexWrap: "wrap", flex: 1 }}>
                <Text style={[TextStyles.t12NY2.copyWith({ lineHeight: Sizes.dp16 }), { flexShrink: 1 }]} numberOfLines={2}>
                  {tips ?? ""}
                </Text>
              </View>
          )}
          {!!duringTime && (
              <View style={[ABCStyles.rowAlignCenter, { flexWrap: "wrap", flex: 1 }]}>
                <Text style={[TextStyles.t12NY2.copyWith({ lineHeight: Sizes.dp16 })]}>{tips ?? ""}</Text>
                <AbcCountDown
                    duringTime={duringTime * 1000}
                    lockFlag={lockFlag}
                    textStyle={TextStyles.t12NY2}
                    onFinish={() => countdownEndsCallback?.()}
                />
                <Text style={TextStyles.t12NY2.copyWith({ lineHeight: Sizes.dp16 })}>{"不可操作"}</Text>
              </View>
          )}
        </View>
        <View
            style={[ABCStyles.rowAlignCenter]}
            onClick={() => {
              !!cancelText ? cancelOperate?.() : showHandleBtn && onClick?.();
            }}
        >
          {!!cancelText && <Text style={TextStyles.t12NB1.copyWith({ lineHeight: Sizes.dp16 })}>{cancelText ?? " "}</Text>}
          {showHandleBtn && (
              <View style={ABCStyles.rowAlignCenter}>
                <Text style={TextStyles.t12NY2.copyWith({ lineHeight: Sizes.dp16 })}>{`去处理`}</Text>
                <AbcSizedBox width={Sizes.dp4} />
                <View style={{ marginTop: Sizes.dp2 }}>
                  <AbcRightArrowIcon color={Colors.Y2} size={Sizes.dp12} />
                </View>
              </View>
          )}
        </View>
      </AbcView>
  );
};
