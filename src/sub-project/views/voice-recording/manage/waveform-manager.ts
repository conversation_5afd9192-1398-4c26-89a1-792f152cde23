/**
 * 语音录制波形动画管理器
 * 负责管理录制过程中的波形数据和动画
 */

import { StateManager } from "./state-manager";
import { DeviceUtils } from "../../../base-ui/utils/device-utils";

const TAG = "WaveformManager";

export class WaveformManager {
    private stateManager: StateManager;
    private animationId: NodeJS.Timeout | null = null;
    private isAnimating = false;

    // 波形动画频率控制相关属性
    private lastWaveformUpdateTime = 0;
    private waveformUpdateInterval = 16; // 统一为16ms间隔（约60fps）
    private waveformDataBuffer: number[] = []; // 波形数据缓冲区
    private lastDataTimestamp = 0;
    private dataInterpolationEnabled = true; // 是否启用数据插值

    // 动态帧率适配相关属性
    private targetFrameRate = 60; // 目标帧率
    private actualFrameRate = 30; // 实际帧率，默认30fps
    private frameRateDetectionEnabled = true; // 是否启用帧率检测
    private lastFrameTime = 0;
    private frameTimeHistory: number[] = []; // 帧时间历史记录
    private maxFrameHistorySize = 10; // 最大历史记录数量
    private performanceMode: "auto" | "safe" | "aggressive" = "auto"; // 性能模式

    constructor(stateManager: StateManager) {
        this.stateManager = stateManager;
        this.initializePerformanceSettings();
    }

    /**
     * 初始化性能设置
     */
    private initializePerformanceSettings(): void {
        // 根据平台设置默认性能模式
        if (DeviceUtils.isAndroid()) {
            this.performanceMode = "safe";
            this.actualFrameRate = 30;
            this.targetFrameRate = 30;
        } else {
            this.performanceMode = "auto";
            this.actualFrameRate = 60;
            this.targetFrameRate = 60;
        }

        this.waveformUpdateInterval = 1000 / this.actualFrameRate;
    }

    /**
     * 开始波形动画
     */
    public startAnimation(): void {
        const currentState = this.stateManager.getState();
        if (!this.isAnimating && currentState.isRecording && !currentState.isPaused) {
            this.isAnimating = true;
            this.animate();
        }
    }

    /**
     * 暂停波形动画
     */
    public pauseAnimation(): void {
        if (this.animationId) {
            clearTimeout(this.animationId);
            this.animationId = null;
        }
        this.isAnimating = false;
    }

    /**
     * 动画循环
     */
    private animate = (): void => {
        const currentTime = Date.now();

        // 帧率检测和自适应调整
        if (this.frameRateDetectionEnabled) {
            this.detectAndAdaptFrameRate(currentTime);
        }

        this.consumeWaveformDataBuffer();

        if (this.isAnimating) {
            const frameInterval = this.getOptimalFrameInterval();
            this.animationId = setTimeout(this.animate, frameInterval);
        }
    };

    /**
     * 检测并自适应调整帧率
     */
    private detectAndAdaptFrameRate(currentTime: number): void {
        if (this.lastFrameTime > 0) {
            const frameTime = currentTime - this.lastFrameTime;
            this.frameTimeHistory.push(frameTime);

            // 保持历史记录在合理范围内
            if (this.frameTimeHistory.length > this.maxFrameHistorySize) {
                this.frameTimeHistory.shift();
            }

            // 每10帧检测一次性能
            if (this.frameTimeHistory.length >= this.maxFrameHistorySize) {
                this.analyzePerformanceAndAdjust();
            }
        }

        this.lastFrameTime = currentTime;
    }

    /**
     * 分析性能并调整帧率
     */
    private analyzePerformanceAndAdjust(): void {
        const avgFrameTime = this.frameTimeHistory.reduce((sum, time) => sum + time, 0) / this.frameTimeHistory.length;

        // 检测是否出现性能问题
        const targetFrameTime = 1000 / this.targetFrameRate;
        const performanceRatio = avgFrameTime / targetFrameTime;

        if (this.performanceMode === "auto") {
            if (performanceRatio > 1.5) {
                // 性能不足，降低帧率
                this.actualFrameRate = Math.max(20, this.actualFrameRate * 0.8);
                console.log(TAG, `Performance issue detected, reducing frame rate to ${this.actualFrameRate}fps`);
            } else if (performanceRatio < 0.8 && this.actualFrameRate < this.targetFrameRate) {
                // 性能充足，尝试提高帧率
                this.actualFrameRate = Math.min(this.targetFrameRate, this.actualFrameRate * 1.1);
                console.log(TAG, `Good performance, increasing frame rate to ${this.actualFrameRate}fps`);
            }

            this.waveformUpdateInterval = 1000 / this.actualFrameRate;
        }
    }

    /**
     * 获取最优帧间隔
     */
    private getOptimalFrameInterval(): number {
        switch (this.performanceMode) {
            case "safe":
                return 34; // 30fps，安全模式
            case "aggressive":
                return 16; // 60fps，激进模式
            case "auto":
            default:
                return Math.max(16, 1000 / this.actualFrameRate);
        }
    }

    /**
     * 消费波形数据缓冲区
     */
    private consumeWaveformDataBuffer = (): void => {
        const currentTime = Date.now();

        const state = this.stateManager.getState();
        if (!state) {
            return;
        }

        const waveformData = [...state.waveformData];

        // 智能缓冲区消费策略
        const consumeCount = this.calculateOptimalConsumeCount();

        if (this.waveformDataBuffer.length > 0) {
            // 根据缓冲区大小和性能情况决定消费数量
            for (let i = 0; i < consumeCount && this.waveformDataBuffer.length > 0; i++) {
                const dataPoint = this.waveformDataBuffer.shift();
                if (dataPoint !== undefined) {
                    waveformData.push(dataPoint);
                }
            }
        } else {
            // 缓冲区为空时，添加静默数据点保持动画连续性
            waveformData.push(0.01);
        }

        // 保持波形数据数组长度不超过150
        if (waveformData.length > 200) {
            const removeCount = waveformData.length - 200;
            waveformData.splice(0, removeCount);
        }

        // 更新波形数据
        this.updateWaveformData(waveformData, true);

        // 更新最后更新时间
        this.lastWaveformUpdateTime = currentTime;
    };

    /**
     * 计算最优的缓冲区消费数量
     */
    private calculateOptimalConsumeCount(): number {
        const bufferSize = this.waveformDataBuffer.length;

        // 如果缓冲区积压过多，需要加速消费
        if (bufferSize > 20) {
            return Math.min(3, Math.ceil(bufferSize / 10)); // 最多一次消费3个
        } else if (bufferSize > 10) {
            return 2; // 中等积压，消费2个
        } else {
            return 1; // 正常情况，消费1个
        }
    }

    /**
     * 更新波形数据
     */
    public updateWaveformData(waveformData: number[], reset = true): void {
        if (reset) {
            this.stateManager.setState({ waveformData });
            return;
        }
        this.stateManager.setState({ waveformData: [...this.stateManager.getState().waveformData, ...waveformData] });
    }

    /**
     * 处理波形数据并应用频率控制
     * 统一不同平台的波形数据更新频率
     * @param dataArray 原始波形数据数组
     * @param timestamp 数据接收时间戳
     */
    public processWaveformDataWithFrequencyControl(dataArray: number[], timestamp: number): void {
        if (!dataArray || dataArray.length === 0) {
            return;
        }

        // 计算数据接收间隔
        const dataInterval = this.lastDataTimestamp > 0 ? timestamp - this.lastDataTimestamp : 0;
        this.lastDataTimestamp = timestamp;

        // 根据平台数据频率进行处理
        if (this.dataInterpolationEnabled && dataInterval > 0) {
            this.processDataWithInterpolation(dataArray, dataInterval);
        } else {
            // 直接添加到缓冲区
            this.waveformDataBuffer.push(...dataArray);
        }

        // 限制缓冲区大小，避免内存溢出
        const maxBufferSize = 200; // 最大缓冲200个数据点
        if (this.waveformDataBuffer.length > maxBufferSize) {
            this.waveformDataBuffer = this.waveformDataBuffer.slice(-maxBufferSize);
        }
    }

    /**
     * 使用插值处理数据，平滑不同平台的频率差异
     * @param dataArray 原始数据数组
     * @param dataInterval 数据接收间隔
     */
    private processDataWithInterpolation(dataArray: number[], dataInterval: number): void {
        // 目标更新间隔是50ms，如果数据间隔大于50ms，需要插值
        if (dataInterval > this.waveformUpdateInterval * 1.5) {
            // iOS平台情况：100ms间隔，需要插值到50ms
            const interpolationCount = Math.floor(dataInterval / this.waveformUpdateInterval);

            // 获取上一个数据点作为插值起点
            const lastValue = this.waveformDataBuffer.length > 0 ? this.waveformDataBuffer[this.waveformDataBuffer.length - 1] : 0;

            // 为每个原始数据点生成插值
            dataArray.forEach((currentValue, index) => {
                if (interpolationCount > 1) {
                    // 在上一个值和当前值之间插值
                    const startValue = index === 0 ? lastValue : dataArray[index - 1];

                    for (let i = 0; i < interpolationCount; i++) {
                        const ratio = i / interpolationCount;
                        const interpolatedValue = startValue + (currentValue - startValue) * ratio;
                        this.waveformDataBuffer.push(interpolatedValue);
                    }
                } else {
                    this.waveformDataBuffer.push(currentValue);
                }
            });
        } else {
            this.waveformDataBuffer.push(...dataArray);
        }
    }

    /**
     * 重置波形频率控制相关状态
     */
    public resetWaveformFrequencyControl(): void {
        this.lastWaveformUpdateTime = 0;
        this.lastDataTimestamp = 0;
        this.lastFrameTime = 0;
        this.frameTimeHistory = [];
        this.initializePerformanceSettings();
    }

    /**
     * 设置性能模式
     */
    public setPerformanceMode(mode: "auto" | "safe" | "aggressive"): void {
        this.performanceMode = mode;
        console.log(TAG, `Performance mode set to: ${mode}`);

        switch (mode) {
            case "safe":
                this.actualFrameRate = 30;
                this.targetFrameRate = 30;
                this.frameRateDetectionEnabled = false;
                break;
            case "aggressive":
                this.actualFrameRate = 60;
                this.targetFrameRate = 60;
                this.frameRateDetectionEnabled = false;
                break;
            case "auto":
                this.frameRateDetectionEnabled = true;
                this.initializePerformanceSettings();
                break;
        }

        this.waveformUpdateInterval = 1000 / this.actualFrameRate;
    }

    /**
     * 获取性能统计信息
     */
    public getPerformanceStats(): {
        actualFrameRate: number;
        targetFrameRate: number;
        bufferSize: number;
        performanceMode: string;
        avgFrameTime: number;
    } {
        const avgFrameTime =
            this.frameTimeHistory.length > 0
                ? this.frameTimeHistory.reduce((sum, time) => sum + time, 0) / this.frameTimeHistory.length
                : 0;

        return {
            actualFrameRate: this.actualFrameRate,
            targetFrameRate: this.targetFrameRate,
            bufferSize: this.waveformDataBuffer.length,
            performanceMode: this.performanceMode,
            avgFrameTime: avgFrameTime,
        };
    }

    /**
     * 启用性能调试日志
     */
    public enablePerformanceDebug(enabled = true): void {
        if (enabled) {
            const logInterval = setInterval(() => {
                if (!this.isAnimating) {
                    clearInterval(logInterval);
                    return;
                }

                const stats = this.getPerformanceStats();
                console.log(TAG, "[PerformanceDebug]", {
                    fps: Math.round(1000 / stats.avgFrameTime),
                    targetFPS: stats.targetFrameRate,
                    actualFPS: stats.actualFrameRate,
                    bufferSize: stats.bufferSize,
                    mode: stats.performanceMode,
                    frameTime: Math.round(stats.avgFrameTime * 100) / 100,
                });
            }, 2000); // 每2秒输出一次性能统计
        }
    }

    /**
     * 清理资源
     */
    public cleanup(): void {
        this.pauseAnimation();
        this.resetWaveformFrequencyControl();
        this.waveformDataBuffer = [];
    }
}
