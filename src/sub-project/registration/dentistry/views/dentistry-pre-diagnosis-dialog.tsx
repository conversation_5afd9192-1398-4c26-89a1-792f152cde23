import React from "react";
import { ScrollView, View } from "@hippy/react";
import { Colors, Sizes } from "../../../theme";
import { MedicalRecordCard } from "../../views/medical-record-card";
import { MedicalRecordUtils } from "../../../outpatient/medical-record-page/utils/medical-record-utils";
import { BottomSheetHelper } from "../../../base-ui/abc-app-library";
import { ToolBar, ToolBarButtonStyle1 } from "../../../base-ui";
import { ABCNavigator } from "../../../base-ui/views/abc-navigator";
import { showBottomPanel } from "../../../base-ui/abc-app-library/panel";
import { EpidemiologicalHistoryInputPage } from "../../../outpatient/medical-record-page/epidemiological-history-input-page";
import _ from "lodash";
import { MedicalRecord, OssUpdateModules } from "../../../base-business/data/beans";
import { SafeAreaBottomView } from "../../../base-ui/safe_area_view";
import { OutpatientContentRefKey } from "../../../outpatient/data/outpatient-const";
import { StringUtils } from "../../../base-ui/utils/string-utils";
import { MedicalRecordItem } from "../../../outpatient/outpatient-views";
import { ImageUpdateView } from "../../../base-ui/image-update-view";
import { AbcImagePicker } from "../../../base-business/image-picker/abc-image-picker";
import { userCenter } from "../../../user-center";

export class DentistryPreDiagnosisDialog extends MedicalRecordCard {
    private imageSize = Sizes.dp48;
    private async _modifyEpidemiologicalHistoryInfo(): Promise<void> {
        const { editable } = this.props;
        if (!editable) return;
        let medicalRecord = this.props?.medicalRecord;
        const info = await showBottomPanel(<EpidemiologicalHistoryInputPage content={medicalRecord?.__EpidemiologicalHistoryObj} />, {
            topMaskHeight: Sizes.dp160,
        });

        if (!_.isNil(info)) {
            medicalRecord = medicalRecord ?? new MedicalRecord();
            if (info == medicalRecord.epidemiologicalHistory) {
                return;
            }
            medicalRecord.epidemiologicalHistory = info as string;
            this.forceUpdate();
        }
    }

    protected async _modifyImageUpload(): Promise<void> {
        await AbcImagePicker.pickImageAndUpload(null, `${userCenter.clinic?.clinicId ?? ""}/${OssUpdateModules.MEDICAL_RECORD}`).then(
            (RSP) => {
                const { medicalRecord, onChange } = this.props;
                if (!!RSP) {
                    const img = {
                        fileName: RSP.url?.split("/")[RSP.url.split("/").length - 1],
                        fileSize: RSP.size!.width * RSP.size!.height,
                        url: RSP.url,
                    };
                    medicalRecord.preDiagnosisAttachments = medicalRecord?.preDiagnosisAttachments ?? [];
                    medicalRecord.preDiagnosisAttachments.push(img);
                    this.forceUpdate();
                    onChange?.(medicalRecord);
                }
            }
        );
    }

    protected async _modifyImageRemove(index: number): Promise<void> {
        const { medicalRecord, onChange } = this.props;
        medicalRecord.preDiagnosisAttachments?.splice(index, 1);
        this.forceUpdate();
        onChange?.(medicalRecord);
    }

    render(): JSX.Element {
        const { chiefComplaint, presentHistory, pastHistory, __EpidemiologicalHistoryObj, physicalExamination, preDiagnosisAttachments } =
            this.props.medicalRecord;
        return (
            <View
                style={{
                    flex: 1,
                    backgroundColor: Colors.prescriptionBg,
                }}
            >
                {BottomSheetHelper.createTitleBar("预诊")}
                <ScrollView
                    style={{
                        flex: 1,
                        backgroundColor: Colors.white,
                        borderRadius: Sizes.dp6,
                        ...Sizes.marginLTRB(Sizes.dp8, Sizes.dp15),
                    }}
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{ paddingTop: !!StringUtils.stringBr2N(chiefComplaint) ? Sizes.dp16 : 0 }}
                >
                    <MedicalRecordItem
                        type={OutpatientContentRefKey.chiefComplaint}
                        key={"chiefComplaint"}
                        title={"主诉"}
                        content={StringUtils.stringBr2N(chiefComplaint)}
                        showRightArrow={true}
                        onClick={() => {
                            this._modifyChiefComplaint().then();
                        }}
                        isEdit={true}
                        isMarginTop16={true}
                    />
                    <MedicalRecordItem
                        key={"presentHistory"}
                        type={OutpatientContentRefKey.presentHistory}
                        title={"现病史"}
                        content={StringUtils.stringBr2N(presentHistory)}
                        showRightArrow={true}
                        onClick={() => {
                            this._modifyPresentHistory().then();
                        }}
                        isEdit={true}
                        isMarginTop16={true}
                    />
                    <MedicalRecordItem
                        key={"pastHistory"}
                        type={OutpatientContentRefKey.pastHistory}
                        title={"既往史"}
                        content={StringUtils.stringBr2N(pastHistory)}
                        showRightArrow={true}
                        onClick={() => {
                            this._modifyPastHistory().then();
                        }}
                        isEdit={true}
                        isMarginTop16={true}
                    />
                    <MedicalRecordItem
                        key={"epidemiologicalHistory"}
                        type={OutpatientContentRefKey.epidemiologicalHistory}
                        title={"流行病史"}
                        content={StringUtils.stringBr2N(MedicalRecordUtils.getEpidemiologicalHistoryStr(__EpidemiologicalHistoryObj))}
                        showRightArrow={true}
                        onClick={() => {
                            this._modifyEpidemiologicalHistoryInfo().then();
                        }}
                        isEdit={true}
                        isMarginTop16={true}
                    />
                    <MedicalRecordItem
                        key={"physicalExamination"}
                        type={OutpatientContentRefKey.physicalExamination}
                        title={"体格检查"}
                        content={StringUtils.stringBr2N(physicalExamination)}
                        showRightArrow={true}
                        onClick={() => {
                            this._modifyPhysicalExamination().then();
                        }}
                        isMarginTop16={true}
                    />
                    <MedicalRecordItem
                        key={"attachment"}
                        type={OutpatientContentRefKey.attachment}
                        title={"附件"}
                        contentView={
                            <ImageUpdateView
                                imageList={preDiagnosisAttachments}
                                ableUpdate={true}
                                imageSize={this.imageSize}
                                contentStyle={{ maxWidth: this.imageSize * 6, flexWrap: "wrap" }}
                                onUpdate={() => {
                                    this._modifyImageUpload();
                                }}
                                onDelete={(index) => {
                                    this._modifyImageRemove(index);
                                }}
                            />
                        }
                        showRightArrow={true}
                        isMarginTop16={true}
                    />
                </ScrollView>
                <ToolBar hideWhenKeyboardShow={true}>
                    <ToolBarButtonStyle1 text={"确定"} onClick={() => ABCNavigator.pop(this.props?.medicalRecord)} />
                </ToolBar>
                <SafeAreaBottomView bottomSafeAreaColor={Colors.white} />
            </View>
        );
    }
}
