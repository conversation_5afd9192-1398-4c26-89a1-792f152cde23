import { JsonMapper, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";
import { GoodsBatchInfoList } from "../data/beans";
import { PostGoodsStocksCheckOrdersRep } from "../../inventory/inventory-check/data/inventory-check-bean";
import { NumberUtils } from "../../common-base-module/utils";
import _ from "lodash";
import { PostStockCheckTaskReqItemsItemBatch } from "../../inventory/data/inventory-bean";
import { UniqueKey } from "../../base-ui";

export class BatchesInsufficientInventoryItem {
    tips?: string;
    name?: string;
    specification?: string;
    manufacture?: string;
    batchId?: string;
    expireDate?: Date;
    unit?: string;
    @JsonProperty({ type: Array, clazz: GoodsBatchInfoList })
    batchList?: GoodsBatchInfoList[];
}
export class BatchesInsufficientInventory {
    /**
     *
     *  构造药品批次库存不足页面显示数据
     * @param options
     *  goodsInfoList ---- 药品盘点具体的数据
     *   needQueryStockGoods --- 当前需要查询库存的药品，业务数据（主要是含追溯码商品）
     *   type --- 业务来源，用于区分是收费、药房
     */
    static batchInsufficientList(options: {
        goodsInfoList: any;
        needQueryStockGoods: any;
        type: "charge" | "dispensing";
    }): BatchesInsufficientInventoryItem[] {
        const { goodsInfoList, needQueryStockGoods, type } = options;
        const list: BatchesInsufficientInventoryItem[] = [];
        if (!goodsInfoList?.length) return [];
        goodsInfoList?.forEach((item: any) => {
            // 找出checkBatchList中盘盈的一项,并且将checkBatchList中除了当前盘盈的项赋值给batchList
            // 可能存在大小单位都扣库存的情况，所以需要进行换算,换算成小单位来显示
            const profitItem = item.checkBatchList?.find((batchItem: any) => {
                const pieceNum = batchItem.pieceNum ?? item.pieceNum ?? 0;
                const stockCheckCount = (batchItem.stockCheckPackageCount || 0) * pieceNum + (batchItem.stockCheckPieceCount ?? 0) ?? 0;
                return stockCheckCount > 0;
            });
            const batchList = item.checkBatchList?.filter((batchItem: any) => {
                const pieceNum = batchItem.pieceNum ?? item.pieceNum ?? 0;
                const stockCheckCount = (batchItem.stockCheckPackageCount || 0) * pieceNum + (batchItem.stockCheckPieceCount ?? 0) ?? 0;
                return stockCheckCount < 0;
            });
            // needQueryStockGoods中的id与item中的keyId对应，找到对应的formItem,如果的formItem的批次信息（收费chargeFormItemBatchInfos、药房dispensingFormItemBatches），则是强锁批次
            const isStrongLockBatch = (item: any): boolean => {
                const matchedFormItem = needQueryStockGoods?.find((t: any) => t.id == item.keyId);
                if (type == "charge") return !!matchedFormItem?.chargeFormItemBatchInfos?.length;
                return !!matchedFormItem?.dispensingFormItemBatches?.length;
            };
            let tips = "以下药品/耗材，已锁定发药库存批次，但系统该批次已无足够库存量，需要盘点后发药";
            if (!isStrongLockBatch(item)) {
                tips = "以下药品/耗材，医保结算时确定为非拆零发药，但系统已无整装库存的批次，需要盘点后发药（依码支付要求）";
            }
            list.push(
                JsonMapper.deserialize(BatchesInsufficientInventoryItem, {
                    tips: tips,
                    name: item?.displayName,
                    specification: item?.displaySpec,
                    manufacture: profitItem?.supplierName,
                    batchId: profitItem?.batchId?.toString(),
                    expireDate: profitItem?.expiryDate,
                    unit: item?.pieceUnit,
                    batchList: batchList,
                })
            );
        });
        return list;
    }

    //     构造自动盘点传参所需数据
    static batchInsufficientListForAutoCheck(options: {
        goodsInfoList: any;
        type: "charge" | "dispensing";
    }): PostGoodsStocksCheckOrdersRep | undefined {
        const { goodsInfoList, type } = options;
        if (!goodsInfoList?.length) return;
        const postReq = new PostGoodsStocksCheckOrdersRep();
        postReq.list = [];
        //  需要将相同goods去重，并且批次也要去重，如果是相同批次的话，那么stockCheckPieceCount、stockCheckPackageCount也要进行累加
        const filterGoodsList = new Map<string, any>();
        goodsInfoList?.map((item: any) => {
            const goodsId = item.id ?? item.goodsId;
            if (filterGoodsList.has(goodsId)) {
                const filterItem = filterGoodsList.get(goodsId);
                const batchList = new Map<string, GoodsBatchInfoList>();
                // 先处理已存在的批次信息
                for (const batch of filterItem?.checkBatchList ?? []) {
                    if (!batch?.batchId) continue;
                    batchList.set(batch.batchId, batch);
                }

                // 再处理当前item的批次信息
                for (const batch of item?.checkBatchList ?? []) {
                    if (!batch?.batchId) continue;
                    if (!batchList.has(batch.batchId)) {
                        batchList.set(batch.batchId, batch);
                    } else {
                        // 需要将batch中的stockCheckPieceCount、stockCheckPackageCount进行累加
                        const oldBatch = batchList.get(batch.batchId);
                        oldBatch!.stockCheckPieceCount = NumberUtils.preciseAdd(
                            oldBatch?.stockCheckPieceCount ?? 0,
                            batch.stockCheckPieceCount
                        );
                        oldBatch!.stockCheckPackageCount = NumberUtils.preciseAdd(
                            oldBatch?.stockCheckPackageCount ?? 0,
                            batch.stockCheckPackageCount
                        );
                    }
                }

                // 更新合并后的批次列表
                filterItem.checkBatchList = Array.from(batchList.values());
            } else {
                filterGoodsList.set(goodsId, item);
            }
        });
        filterGoodsList.forEach((item) => {
            const _item = _.cloneDeep(item);
            const goodsId = item.id ?? item.goodsId;
            postReq.list!.push({
                batchs: _item.checkBatchList?.map((batchItem: any) => {
                    const batch = new PostStockCheckTaskReqItemsItemBatch();
                    batch.batchId = batchItem.batchId;
                    batch.batchNo = batchItem.batchNo;
                    batch.packageCount = batchItem.packageCount ?? 0;
                    batch.pieceCount = batchItem.pieceCount ?? 0;
                    batch.changedPackageCount = batchItem.stockCheckPackageCount ?? 0;
                    batch.changedPieceCount = batchItem.stockCheckPieceCount ?? 0;
                    batch.goodsId = goodsId;
                    return batch;
                }),
            });
        });
        //过滤掉batchs为空的数据
        postReq.list = postReq.list?.filter((item) => !!item.batchs?.length);
        postReq.orderClientUniqKey = UniqueKey();
        postReq.opType = 1;
        // 发药单只会存在一个药房号，但是收费单不一定
        if (type == "dispensing") postReq.pharmacyNo = goodsInfoList?.[0]?.pharmacyNo;
        return postReq;
    }
}
