/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/6/17
 *
 * @description
 */

import { ABCApiNetwork } from "../../net";
import { Age, DeliveryInfo, MemberInfo, Patient } from "./beans";
import { Subject } from "rxjs";
import { JsonMapper, JsonProperty } from "../../common-base-module/json-mapper/json-mapper";

class PatientsList {
    @JsonProperty({ type: Array, clazz: Patient })
    list?: Array<Patient>;
}

class UpdatePatientByIdReq {
    name?: string;
    birthday?: string;
    sex?: string;
    @JsonProperty({ type: Age })
    age?: Age;
    countryCode?: string; // 国家编码
    mobile?: string;
    idCard?: string; // 身份证
    idCardType?: string;
    addressProvinceId?: string;
    addressProvinceName?: string; // 省份
    addressCityId?: string;
    addressCityName?: string; // 城市
    addressDistrictId?: string;
    addressDistrictName?: string; // 县区
    addressDetail?: string; // 详细地址
    addressGeo?: string;
    sourceId?: string; // 源ID
    sourceFrom?: string;
    remark?: string; // 备注
    sn?: string; // 档案号
    profession?: string; // 职务
    company?: string; // 工作单位
    pastHistory?: string; // 回传既往史

    marital?: number; //婚姻
    weight?: number; //体重
    visitReason?: string; //到店原因
    ethnicity?: string; //民族
    memberTypeId?: string; // 会员等级id
}

export class PatientAgent {
    static patientChangeObserver = new Subject<Patient>();

    static getMemberInfoById(memberId: string): Promise<MemberInfo | undefined> {
        return ABCApiNetwork.get(`patients/${memberId}/member`, {
            clazz: Patient,
        }).then((rsp) => rsp?.memberInfo);
    }

    /// 添加收货地址
    static async createDeliverInfo(patientId: string, info: DeliveryInfo, chainId: string): Promise<DeliveryInfo> {
        const body = info;
        body["chainId"] = chainId ?? "";
        return await ABCApiNetwork.post(`patients/${patientId}/deliveryInfo`, {
            body: body,
            clazz: DeliveryInfo,
        });
    }

    /// 更新收货地址
    static async updateDeliverInfo(patientId: string, info: DeliveryInfo): Promise<DeliveryInfo> {
        return await ABCApiNetwork.put(`patients/${patientId}/deliveryInfo/${info.id}`, {
            body: info,
            clazz: DeliveryInfo,
        });
    }

    /// 删除收货地址
    static async deleteDeliverInfo(patientId: string, deliverId: string): Promise<DeliveryInfo> {
        return await ABCApiNetwork.delete(`patients/${patientId}/deliveryInfo/${deliverId}`, { clazz: DeliveryInfo });
    }

    // 搜索患者（顾客推荐-顾客信息）
    static async queryPatients(key: string): Promise<PatientsList> {
        return ABCApiNetwork.get(`patients/query`, {
            queryParameters: { key },
            clazz: PatientsList,
        });
    }

    public static async updatePatientById(patientId: string, patient: Patient): Promise<Patient> {
        const req = JsonMapper.deserialize(UpdatePatientByIdReq, { ...patient, birthday: patient.birthday ?? "" });

        if (!req.countryCode) {
            req.countryCode = "86";
        }

        if (patient.address != null) {
            req.addressProvinceId = patient.address.addressProvinceId ?? "";
            req.addressProvinceName = patient.address.addressProvinceName ?? "";
            req.addressCityId = patient.address.addressCityId ?? "";
            req.addressCityName = patient.address.addressCityName ?? "";
            req.addressDistrictId = patient.address.addressDistrictId ?? "";
            req.addressDistrictName = patient.address.addressDistrictName ?? "";
            req.addressDetail = patient.address.addressDetail;
            req.addressGeo = patient.address.addressGeo;
        }
        if (patient.patientSource?.relatedType != undefined) {
            req.sourceId = patient.patientSource.sourceFrom ?? patient.patientSource?.id;
        } else {
            req.sourceId = patient.patientSource?.id;
            req.sourceFrom = patient.patientSource?.sourceFrom;
        }
        if (!!patient.memberInfo?.memberTypeInfo?.memberTypeId) {
            req.memberTypeId = patient.memberInfo?.memberTypeInfo?.memberTypeId;
        }
        const rsp = await ABCApiNetwork.put(`patients/${patientId}`, {
            body: req,
            clazz: Patient,
        });
        PatientAgent.patientChangeObserver.next(rsp);
        return rsp;
    }

    public static async createPatient(patient: Patient): Promise<Patient> {
        const req = JsonMapper.deserialize(UpdatePatientByIdReq, { ...patient, birthday: patient.birthday ?? "" });

        if (!req.countryCode) {
            req.countryCode = "86";
        }

        if (patient.address != null) {
            req.addressProvinceId = patient.address.addressProvinceId ?? "";
            req.addressProvinceName = patient.address.addressProvinceName ?? "";
            req.addressCityId = patient.address.addressCityId ?? "";
            req.addressCityName = patient.address.addressCityName ?? "";
            req.addressDistrictId = patient.address.addressDistrictId ?? "";
            req.addressDistrictName = patient.address.addressDistrictName ?? "";
            req.addressDetail = patient.address.addressDetail;
            req.addressGeo = patient.address.addressGeo;
        }
        if (patient.patientSource?.relatedType != undefined) {
            req.sourceId = patient.patientSource.sourceFrom ?? patient.patientSource?.id;
        } else {
            req.sourceId = patient.patientSource?.id;
            req.sourceFrom = patient.patientSource?.sourceFrom;
        }
        if (!!patient.memberInfo?.memberTypeInfo?.memberTypeId) {
            req.memberTypeId = patient.memberInfo?.memberTypeInfo?.memberTypeId;
        }
        const rsp = await ABCApiNetwork.post("patients", {
            body: req,
            clazz: Patient,
        });
        PatientAgent.patientChangeObserver.next(rsp);
        return rsp;
    }
}
