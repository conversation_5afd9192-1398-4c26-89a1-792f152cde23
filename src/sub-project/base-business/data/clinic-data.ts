/**
 * create by dengjie
 * desc:
 * create date 2020/6/11
 */
import { ChainInfo } from "../../outpatient/data/outpatient-beans";
import { JsonProperty } from "../../common-base-module/json-mapper/json-mapper";

//最近登录过的诊所
export class RecentUsedClinicItem {
    clinicId?: string;
    clinicName?: string;
    clinicType?: number;
    lastLoginDate?: string;
}

export class Departments {
    employeeId?: string;
    id?: string;
    chainId?: string;
    clinicId?: string;
    name?: string;
    type?: number;
    isDefault?: number;
    mainMedical?: string;
    secondMedical?: string;
    principal?: string;

    customId?: string;
    isClinical?: number;
}

export class ClinicInfo {
    employeeId?: string;
    clinicId?: string;
    roleId?: number;
    moduleIds?: string;
    role?: string;
    status?: number;
}

export class Tag {
    employeeId?: string;
    id?: string;
    name?: string;
}

export class DoctorDetailInfo {
    id?: string;
    name?: string;
    mobile?: string;
    headImgUrl?: string;
    handSign?: string;
    wechatSubscribe?: number;
    wechatNickName?: string;
    namePy?: string;
    namePyFirst?: string;
    sort?: number;
    clinicInfo?: ClinicInfo;
    chainInfo?: ChainInfo;
    @JsonProperty({ type: Array, clazz: Tag })
    tags?: Tag[];
    @JsonProperty({ type: Array, clazz: Departments })
    departments?: Departments[];
}

export class RoleItem {
    id?: string;
    moduleIds?: string[];
    name?: string;
}

export class ModuleItem {
    id?: string;
    name?: string;
    parentId?: string;
    sort?: number;
}

export enum ModuleRolesId {
    ROLE_ADMIN_ID = 0, //管理员
    ROLE_DOCTOR_ID = 1, //医生
    ROLE_NURSE_ID = 2, //护士
    ROLE_SURVEYOR_ID = 3, //检验师
    ROLE_PHYSIOTHERAPIST_ID = 4, //理疗师、治疗师
    ROLE_DOCTOR_ASSIST_ID = 5, //医助
    ROLE_CLERK_ID = 6, //其他
    ROLE_STOCK_MANAGER_ID = 7, //库存管理员
    ROLE_OPTOMETRY_ID = 9, //视光师
    ROLE_INSPECT_ID = 10, //检查师
    ROLE_CONSULTANT_ID = 11, //咨询师
    ROLE_HOSPITAL_DOCTOR_ID = 102, //住院医生
    _HOSPITAL_NURSE_ID = 104, //住院护士
    ROLE_CASHIER_ID = 110, //收费员
    ROLE_DISPENSER_ID = 111, //发药员
}
export class ConsultantList {
    clinicId?: string;
    code?: number;
    countryCode?: string;
    doctorIdCardNo?: string;
    employeeId?: string;
    employeeName?: string;
    employeeNamePy?: string;
    employeeNamePyFirst?: string;
    isDoctor?: number;
    mobile?: string;
    moduleIds?: string;
    nationalDoctorCode?: string;
    roles?: number[];
    status?: number;
}
export class BusinessScopeList {
    scene?: number;
    id?: string;
    parentId?: string;
    name?: string;
    displayName?: string;
    children?: BusinessScopeList[];
    _displayName?: string; //终端自定义字段，用于拼接对应父级的name
}
class BusinessScopeDict {
    @JsonProperty({ type: Array, clazz: BusinessScopeList })
    organBusinessScopeList?: BusinessScopeList[]; //机构
    @JsonProperty({ type: Array, clazz: BusinessScopeList })
    goodsBusinessScopeList?: BusinessScopeList[]; //商品
    @JsonProperty({ type: Array, clazz: BusinessScopeList })
    supplierBusinessScopeList?: BusinessScopeList[]; //供应商
}
export class ClinicDictionaryInfo {
    @JsonProperty({ type: BusinessScopeDict })
    businessScopeDict?: BusinessScopeDict; // 所属经营范围
}

/**
 * 员工业务场景身份验证-授权类型
 */
export enum EmployeeBusAuthGrantType {
    // 密码模式
    PASSWORD = "password",
    // 验证码模式
    VERIFY_CODE = "verify_code",
    // 授权码模式
    AUTHORIZATION_CODE = "authorization_code",
}

export enum EmployeeBusQrCodeStatus {
    INIT = 0, // 初始化
    SCANNED = 10, // 已扫
    EXPIRED = 80, // 过期
    CANCELLED = 99, // 取消
}
export class EmployeeBusAuthRsp {
    qrCode?: string; // 二维码，可以直接展示二维码。微信场景码可以返回
    qrCodeSceneKey?: string; // 场景key，用来查询redis
    qrCodeUrl?: string; // 二维码链接, 二维码对应的url，需要转换才能变成二维码
    status?: EmployeeBusQrCodeStatus; // 状态;
}

export class ClinicAllEmployeesItem {
    employeeId?: string;
    modules?: string;
    status?: number;
    countryCode?: string;
    departments?: string;
    nationalDoctorCode?: string;
    nationalDoctorCodeFillStatus?: string;
    nationalNurseCode?: string;
    nationalNurseCodeFillStatus?: string;
    qwUserId?: string;
    shebaoScoreSum?: number;
    shebaoDrStatus?: string;
    shebaoScoreYear?: string;
    shebaoScoreUpdateTime?: string;
    shebaoScoreTotal?: number;
    roles?: string;
    employeeName?: string;
    employeeMobile?: string;
    employeeHeadImgUrl?: string;
    employeeNamePy?: string;
    employeeNamePyFirst?: string;
    isAdmin?: boolean;
    hasFilledNationalNurseCode?: boolean;
    hasFilledNationalDoctorCode?: boolean;
    roleIds?: number[];
}

class PracticeSubjectItem {
    id?: string;
    name?: string;
    level?: number;
    _score?: number;
    checked?: boolean;
    parentId?: string;
    isDeleted?: number;
    departmentId?: string;
    addressCityId?: string;
}
class AdminListItem {
    id?: string;
    name?: string;
    mobile?: string;
    countryCode?: string;
    status?: number;
    created?: string;
}
class CoClinicCooperationOrgan {
    id?: string;
    shortId?: string;
    name?: string;
    shortName?: string;
    parentId?: string;
    parentShortId?: string;
    nodeType?: number;
    viewMode?: number;
    hisType?: number;
    busMode?: number;
    status?: number;
    level?: number;
    expiredAt?: string;
    shortNamePyFirst?: string;
    namePy?: string;
    namePyFirst?: string;
    contactPhone?: string;
    backupContactPhone?: string;
    logo?: string;
    backupContactMobile?: string;
    qrUrl?: string;
    addressProvinceId?: string;
    addressProvinceName?: string;
    addressCityId?: string;
    addressCityName?: string;
    addressDistrictId?: string;
    addressDistrictName?: string;
    addressDetail?: string;
    addressGeo?: string;
    createdDate?: string;
    scc?: string;
    nationalCode?: string;
    source?: string;
    innerFlag?: number;
    editionStatus?: number;
    supervisionFlag?: number;
    editionId?: string;
    classCode?: number;
    degreeCode?: number;
    category?: string;
    @JsonProperty({ type: Array, clazz: PracticeSubjectItem })
    practiceSubject?: PracticeSubjectItem[];
    qwCorpId?: string;
    busSupportFlag?: number;
    @JsonProperty({ type: Array, clazz: AdminListItem })
    adminList?: AdminListItem[];
    lastModifiedDate?: string;
    createdUserId?: string;
    lastModifiedUserId?: string;
    regionId?: string;
    isOpenScrm?: number;
    isOpenEyeBus?: number;
    isOpenOralBus?: number;
}
export class CoClinicListByClinic {
    organ?: any;
    @JsonProperty({ clazz: CoClinicCooperationOrgan })
    cooperationOrgan?: CoClinicCooperationOrgan;
    createTime?: Date;
}
