/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020/8/3
 *
 * @description 用于收集一些未归类的native调用
 *
 */

import { callNativeWithPromise } from "@hippy/react";
import { LogLevel } from "../../common-base-module/log/log-utils";
import { AnyType } from "../../common-base-module/common-types";
import { AppInfo } from "../config/app-info";
import { Version } from "../../base-ui/utils/version-utils";

const kModuleName = "MixInvokeMethod";

export class AbcPlatformMix {
    /**
     * 设置hippy调试开关
     * @param enable
     */
    static setHippyDebug(enable: boolean): Promise<void> {
        return this._invokeMethod("setHippyDebug", { enable: enable });
    }

    public static setLogDebugLevel(level: LogLevel): Promise<void> {
        return this._invokeMethod("setLogDebugLevel", { level: level });
    }

    /**
     * 重新加载
     */
    public static reload(): Promise<void> {
        return this._invokeMethod("reload").then();
    }

    /**
     * 读取native端的asset资源
     * @param asset
     */
    public static getAssetAsString(asset: string): Promise<string> {
        return this._invokeMethod("getAssetAsString", { asset: asset });
    }

    /**
     * 计算文件的md5
     * @param file
     */
    static md5WithFile(file: string): Promise<string> {
        return this._invokeMethod("md5WithFile", { file: file });
    }

    /**
     * 解压指定文件
     * @param zipFile
     * @param outDir
     */
    static async unzip(zipFile: string, outDir: string): Promise<boolean> {
        return this._invokeMethod("unzip", { zipFile: zipFile, outDir: outDir });
    }

    /**
     * 调用native打开url
     * @param url
     * @param params
     */
    public static async launch(url: string, params?: { universalLinksOnly?: boolean }): Promise<void> {
        return this._invokeMethod("launch", {
            url: url,
            ...(params ?? {}),
        });
    }

    /**
     * 获取图像文件的像素大小
     * @param path
     */
    public static getImageSize(path: string): Promise<{ width: number; height: number }> {
        return this._invokeMethod("getImageSize", {
            path: path,
        });
    }

    /**
     *
     * Apk 安装
     * @param file
     */
    public static install(file: string): Promise<void> {
        return this._invokeMethod("install", { file: file });
    }

    /**
     * 退出应用
     */
    public static exitApp(): Promise<void> {
        return this._invokeMethod("exitApp", {});
    }

    /**
     * 设置灰度标记到
     */
    public static setGrayFlag(grayFlag: string): Promise<void> {
        //1.9.1版本才开始支持此接口
        const lessThan191 = new Version(AppInfo.appVersion).compareTo(new Version("1.9.1")) < 0;
        if (lessThan191) return Promise.resolve();

        return this._invokeMethod("setGrayFlag", { grayFlag: grayFlag });
    }

    public static setRegionHost(regionHost: string): Promise<void> {
        //2.9版本才开始支持此接口
        const lessThan290 = new Version(AppInfo.appVersion).compareTo(new Version("2.9.0")) < 0;
        if (lessThan290) return Promise.resolve();

        return this._invokeMethod("setRegionHost", { regionHost: regionHost });
    }

    /**
     * 通知native端，hippy首帧显示出来了
     */
    public static hippyFirstFrameReady(): void {
        this._invokeMethod("hippyFirstFrameReady");
    }

    public static appIsFront(): Promise<boolean> {
        return this._invokeMethod("appIsFront", {});
    }

    public static _invokeMethod(methodName: string, params?: { [key: string]: any }): Promise<AnyType> {
        return callNativeWithPromise(kModuleName, "invokeMethod", methodName, params ?? {});
    }
}
