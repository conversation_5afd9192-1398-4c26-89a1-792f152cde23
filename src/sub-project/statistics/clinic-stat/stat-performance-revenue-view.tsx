/**
 * create by <PERSON><PERSON>
 * desc: 开单业绩
 * create date 2021/7/2
 */
import { ABCNetworkPageContentStatus, BaseBlocNetworkView, NetworkView } from "../../base-ui/base-page";
import { StatPerformanceRevenueViewBloc } from "./stat-performance-revenue-view-bloc";
import { ScrollView, Text, View } from "@hippy/react";
import React from "react";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { userCenter } from "../../user-center";
import { ABCUtils } from "../../base-ui/utils/utils";
import { BaseComponent } from "../../base-ui/base-component";
import { StatFilterExpansionView } from "../views/stat-filter-view";
import { AbcEmptyItemView } from "../../base-ui/views/empty-view";
import { UIUtils } from "../../base-ui/utils";
import IconFontView from "../../base-ui/iconfont/iconfont-view";
import { StatSelectPage } from "./stat-select-page";
import { AbcView } from "../../base-ui/views/abc-view";
import { SAVE_STATISTICS_REVENUE_FILTER_STATUS, StatFilter, StatFilterEnumID } from "../utils/statistics-utils";
import { ChainClinicsInfo } from "../../base-business/data/clinic-agent";
import { PerformanceStatItem } from "../views/stat-views";
import { TimeUtils } from "../../common-base-module/utils";
import { GridView } from "../../base-ui/views/grid-view";
import { BlocHelper } from "../../bloc/bloc-helper";
import _ from "lodash";
import { SizedBox } from "../../base-ui";
import { PostShareReportStatPerformanceRevenueData } from "../data/statistics-bean";
import { Range } from "../../base-ui/utils/value-holder";
import { EmployeeTypeEnum } from "../data/statistics-agent";
import { PerformanceStatDialog } from "../views/performance-stat-layout-dialog";
import { DeviceUtils } from "../../base-ui/utils/device-utils";
import abcI18Next from "../../language/config";
import { GetClinicBasicSetting } from "../../data/online-property-config-provder";

export interface StatPerformanceRevenueViewProps {
    date?: Date;
    clinicBasicSetting?: GetClinicBasicSetting;
}
interface StatPerformanceRevenueViewStates {
    selected: boolean;
    selectClinic: boolean;
}

export class StatPerformanceRevenueView extends BaseBlocNetworkView<
    StatPerformanceRevenueViewProps,
    StatPerformanceRevenueViewBloc,
    StatPerformanceRevenueViewStates
> {
    protected filters: StatFilter;
    private clinicNameRef: View | null = null;

    constructor(props: StatPerformanceRevenueViewProps) {
        super(props);
        this.filters = new StatFilter({
            canIncludeWriter: true,
            canRegistration: true,
            canSelectEmployee: true,
            sharedPreferencesKey: SAVE_STATISTICS_REVENUE_FILTER_STATUS,
        });
        this.bloc = new StatPerformanceRevenueViewBloc({ date: props.date });
        this.state = {
            selected: false,
            selectClinic: false,
        };
    }

    componentDidMount(): void {
        BlocHelper.connectLoadingStatus(this.bloc, this);
    }

    public getShareData(): PostShareReportStatPerformanceRevenueData {
        const state = this?.bloc.currentState;
        return {
            dateRange: new Range(state?.timeRange.start ?? new Date(), state?.timeRange.end ?? new Date()),
            achievement: this?.bloc.currentState.achievement,
            clinicName: !!userCenter.clinic?.isChainAdminClinic ? this.getClinicName() : "",
            hospitalBillingPerformanceList: state?.hospitalBillingPerformanceList,
        };
    }

    async onChangeClinicFilter(): Promise<void> {
        if (!userCenter.clinic?.isChainAdminClinic) return;
        this.setState({ selected: true });
        const bloc = this.bloc,
            state = bloc.currentState;
        const layout = await UIUtils.measureInWindow(this.clinicNameRef);
        const offsetY = layout.y + layout.height - (DeviceUtils.isAndroid() ? Sizes.dp10 : 0);
        const clinics = await StatSelectPage.show(state.selectClinic, this.bloc.currentState.currentClinic?.id ?? "", offsetY);
        if (clinics) {
            bloc.requestUpdateSelectClinic(clinics as ChainClinicsInfo);
            this.setState({ selectClinic: !!clinics.id });
        }
        this.setState({ selected: false });
    }

    // 收入汇总视图
    protected _renderStatSummaryBaseDataView(): JSX.Element {
        const state = this.bloc.currentState,
            { achievement, commissionTypeStr, commissionCaliberStr, commissionTimingStr, cardExpenseProvision } = state;
        const summary = achievement?.summary;
        const totalAmount = commissionCaliberStr == "原价" ? summary?.commissionAmount ?? 0 : summary?.amount ?? 0; // 计提金额

        const patientCount = summary?.patientCount ?? 0; // 客量
        const isDrugstoreButler = !!userCenter.clinic?.isDrugstoreButler;
        const saleOrBill = isDrugstoreButler ? "销售" : "开单";
        const list = [
            {
                title: commissionTypeStr,
                count: totalAmount,
                layOut: () => {
                    PerformanceStatDialog.showPerformanceStatLayoutDialog({
                        title: saleOrBill + "计提金额",
                        content: `用来评估员工${saleOrBill}业绩，为计算提成的基础。提成金额=计提金额*提成比例`,
                        subTitle: `统计口径：${isDrugstoreButler ? `按${commissionCaliberStr}计提` : ""}`,
                        subContent: isDrugstoreButler
                            ? []
                            : [
                                  `·按${commissionCaliberStr}计提`,
                                  `·${commissionTimingStr}计提`,
                                  `${cardExpenseProvision ? `·${cardExpenseProvision}` : ""}`,
                              ],
                    });
                },
            },
            {
                title: "客量",
                count: patientCount,
            },
        ];
        return (
            <GridView
                style={{ borderRadius: Sizes.dp6, backgroundColor: Colors.statTopBg, paddingVertical: Sizes.dp16 }}
                crossAxisCount={2}
            >
                {list.map((item, index) => {
                    return (
                        <PerformanceStatItem
                            key={index}
                            title={item.title}
                            count={item.count!}
                            layOut={item.layOut}
                            showNum={index == 1}
                            showRightLine={index == 0}
                            countStyle={[TextStyles.t20BW]}
                            titleStyle={[TextStyles.t12NW]}
                        />
                    );
                })}
            </GridView>
        );
    }

    renderContent(): JSX.Element {
        const { date } = this.props;
        return (
            <View style={{ flex: 1 }}>
                {!date ? this._renderFilterView() : this._renderDateView()}
                <ScrollView
                    style={{ flex: 1, paddingHorizontal: Sizes.dp8, backgroundColor: Colors.transparent }}
                    contentContainerStyle={{ flexGrow: 1 }}
                    showsVerticalScrollIndicator={false}
                >
                    <SizedBox height={Sizes.dp8} />
                    {this._renderStatSummaryBaseDataView()}
                    <SizedBox height={Sizes.dp18} />
                    <BillingPerformanceListView />
                    <SizedBox height={Sizes.dp34} />
                </ScrollView>
            </View>
        );
    }
    private getClinicName(): string {
        const state = this.bloc.currentState;
        const currentClinic = state.selectClinic?.find((item) => item.id == (state.currentClinic?.id ?? ""));
        let showClinicName: string | undefined = "";
        if (!_.isEmpty(currentClinic) && !!currentClinic?.shortName?.length) {
            showClinicName = currentClinic?.shortName;
        } else {
            showClinicName = currentClinic?.name;
        }
        return showClinicName ?? userCenter.clinic?.displayName ?? "";
    }
    protected _renderFilterView(): JSX.Element {
        const state = this.bloc.currentState;
        const { selected, selectClinic } = this.state;
        // 使用后台配置
        this.filters.filters.map((group) => {
            group.filters?.map((item) => {
                if (item.id == StatFilterEnumID.includingRegistrationID) {
                    item.title = `含${state.clinicBasicSetting?.registrationFeeStr}`;
                } else if (item.id == StatFilterEnumID.excludingRegistrationID) {
                    item.title = `不含${state.clinicBasicSetting?.registrationFeeStr}`;
                }
                return item;
            });
        });

        return (
            <View
                style={[ABCStyles.rowAlignCenter, { paddingHorizontal: Sizes.listHorizontalMargin, backgroundColor: Colors.transparent }]}
                ref={(ref) => (this.clinicNameRef = ref)}
                collapsable={false}
            >
                {!!userCenter.clinic?.isChainAdminClinic && (
                    <AbcView
                        style={{ flex: 4, flexShrink: 1, ...ABCStyles.rowAlignCenter }}
                        onClick={this.onChangeClinicFilter.bind(this)} // 门店筛选
                    >
                        <Text
                            numberOfLines={1}
                            style={[
                                TextStyles.t14NB.copyWith(selected || selectClinic ? { color: Colors.mainColor } : {}),
                                { flexShrink: 1 },
                            ]}
                            ellipsizeMode={"tail"}
                        >
                            {this.getClinicName()}
                        </Text>
                        <IconFontView
                            name={"Dropdown_Triangle"}
                            size={Sizes.dp14}
                            color={selected || selectClinic ? Colors.mainColor : Colors.T6}
                            style={{ transform: [{ rotate: `${selected ? 180 : 0}deg` }] }}
                        />
                    </AbcView>
                )}

                <StatFilterExpansionView
                    selectEmployees={state.selectAchievementEmployee ?? state.selectIncludeWriterEmployee}
                    selectEmployeesType={state.selectAchievementEmployee ? EmployeeTypeEnum.SELLER : EmployeeTypeEnum.AGENT}
                    achievementEmployeesSelectionData={state.achievementEmployeesSelectionData}
                    includeEmployeesSelectionData={state.includeEmployeesSelectionData}
                    filters={this.filters}
                    height={Sizes.listItemHeight}
                    onChange={(filters) => {
                        if (!filters) return;
                        this.filters.fillSelectAttrs(filters, true);
                        this.bloc.requestUpdateFilter(filters);
                    }}
                />
            </View>
        );
    }

    private _renderDateView(): JSX.Element {
        const { date } = this.props;
        return (
            <View style={[ABCStyles.rowAlignCenterSpaceBetween, { paddingHorizontal: Sizes.listHorizontalMargin }]} collapsable={false}>
                <View style={{ height: Sizes.listItemHeight, justifyContent: "center" }}>
                    <Text style={TextStyles.t14NT1}>{`${date?.format("yyyy年MM月dd日") ?? ""} ${TimeUtils.getDayOfWeek(date)}`}</Text>
                </View>
            </View>
        );
    }
}

// 开单业绩
class BillingPerformanceListView extends NetworkView {
    static contextType = StatPerformanceRevenueViewBloc.Context;

    componentDidMount() {
        super.componentDidMount();
        const bloc = StatPerformanceRevenueViewBloc.fromContext(this.context);
        bloc.state
            .subscribe((state) => {
                let status = ABCNetworkPageContentStatus.show_data;
                if (state.loading) {
                    status = ABCNetworkPageContentStatus.loading;
                } else if (state.loadError) {
                    status = ABCNetworkPageContentStatus.error;
                }
                this.setContentStatus(status, state.loadError);
            })
            .addToDisposableBag(this);
    }

    renderContent() {
        const { achievement, commissionCaliberStr, currentClinic } = StatPerformanceRevenueViewBloc.fromContext(this.context).currentState;
        return (
            <View style={[Sizes.listBorderPadding, { flexGrow: 1, borderRadius: Sizes.dp6, backgroundColor: Colors.white }]}>
                <Text style={[TextStyles.t18MB, { lineHeight: Sizes.dp26, marginTop: Sizes.dp20 }]}>业绩名单</Text>
                {achievement?.data?.length == 0 ? (
                    <AbcEmptyItemView tips={"暂无数据"} style={{ paddingTop: Sizes.dp140 }} />
                ) : (
                    <View>
                        {achievement?.data
                            ?.sort((a, b) => b.amount - a.amount)
                            ?.map((item, index) => {
                                return (
                                    <_BillingPerformanceView
                                        key={item.employeeName + index}
                                        isWriter={!!item.isWriter}
                                        employeeName={item.employeeName!}
                                        name={userCenter.clinic?.isChainAdminClinic ? (!currentClinic?.id ? item.clinicName : "") : ""}
                                        amount={commissionCaliberStr == "原价" ? item.commissionAmount! ?? 0 : item.amount! ?? 0}
                                        patientCount={item.patientCount!}
                                        drawTopLine={index != 0}
                                    />
                                );
                            })}
                    </View>
                )}
            </View>
        );
    }
}

// 开单业绩列表
interface _BillingPerformanceViewProps {
    employeeName: string;
    name: string;
    amount: number;
    patientCount: number;
    drawTopLine: boolean;
    isWriter: boolean;
}

class _BillingPerformanceView extends BaseComponent<_BillingPerformanceViewProps> {
    constructor(props: _BillingPerformanceViewProps) {
        super(props);
    }

    render() {
        const { employeeName, name, amount, patientCount, isWriter } = this.props;

        return (
            <View
                key={name}
                style={{ borderBottomWidth: Sizes.dpHalf, borderBottomColor: Colors.dividerLineColor, paddingVertical: Sizes.dp16 }}
            >
                <View style={{ ...ABCStyles.rowAlignCenter }}>
                    <Text style={{ flex: 1, minWidth: Sizes.dp64, ...(isWriter ? TextStyles.t16NT6 : TextStyles.t16NT0) }}>
                        {employeeName}
                    </Text>
                    <View
                        style={{
                            flexDirection: "row",
                            alignItems: "flex-end",
                            justifyContent: "flex-end",
                            minWidth: Sizes.dp76,
                            marginRight: Sizes.dp8,
                        }}
                    >
                        <Text style={[isWriter ? TextStyles.t12NT6 : TextStyles.t12NB, { marginBottom: Sizes.dp1 }]}>
                            {abcI18Next.t("￥")}
                        </Text>
                        <Text style={{ ...(isWriter ? TextStyles.t16NT6 : TextStyles.t16NT0) }}>{ABCUtils.formatPrice(amount)}</Text>
                    </View>
                    <Text style={{ minWidth: Sizes.dp63, ...(isWriter ? TextStyles.t16NT6 : TextStyles.t16NT1), textAlign: "right" }}>
                        {String(patientCount)}人
                    </Text>
                </View>
                {!!name && (
                    <Text style={{ flex: 1, ...TextStyles.t14NT6, marginTop: Sizes.dp4 }} numberOfLines={1}>
                        {name}
                    </Text>
                )}
            </View>
        );
    }
}
