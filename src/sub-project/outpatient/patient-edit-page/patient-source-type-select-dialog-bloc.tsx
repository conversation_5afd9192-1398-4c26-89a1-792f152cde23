/**
 * 成都字节星球科技公司
 * <AUTHOR>
 * @date 2020-03-20
 *
 * @description
 */

import { Bloc, BlocEvent } from "../../bloc";
import React from "react";
import { EventName } from "../../bloc/bloc";
import { of, Subject } from "rxjs";
import { debounce, switchMap } from "rxjs/operators";
import { ABCError } from "../../common-base-module/common-error";
import { BaseLoadingState } from "../../bloc/bloc-helper";
import { ClinicAgent } from "../../base-business/data/clinic-agent";
import { Patient, PatientSource, PatientsSourceTypesItem } from "../../base-business/data/beans";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { PatientAgent } from "../../base-business/data/patient-agent";
import _ from "lodash";
import { delayed } from "../../common-base-module/rxjs-ext/rxjs-ext";
import { DoctorDetailInfo } from "../../base-business/data/clinic-data";
import { GetClinicMedicalRecordConfig, OnlinePropertyConfigProvider } from "../../data/online-property-config-provder";

export enum SourceType {
    REFERRAL_DOCTOR = "转诊医生",
    DOCTOR_ADVISE = "医生推荐",
    DOCTOR_GUIDE_ADVISE = "员工推荐",
    CUSTOMER_ADVISE = "顾客推荐", // 原亲友推荐
}

export interface SourceSubType {
    id: string;
    name: string;
}

export class State extends BaseLoadingState {
    sourceTypes: PatientsSourceTypesItem[] = []; // 来源推荐 主选项
    subTypes: PatientsSourceTypesItem[] = []; // 来源推荐 子选项

    currentSource?: PatientSource;

    matchingPersonnel?: DoctorDetailInfo[] = []; // 医生、导师推荐搜索结果
    clinicFieldConfig?: GetClinicMedicalRecordConfig;

    hasSubType(data: PatientsSourceTypesItem): boolean {
        const customType = data.children?.filter((item) => item.children?.length && item.children.length !== 0); // 自定义类型

        return (
            SourceType.REFERRAL_DOCTOR === data.name ||
            SourceType.DOCTOR_ADVISE === data.name ||
            SourceType.DOCTOR_GUIDE_ADVISE === data.name ||
            SourceType.CUSTOMER_ADVISE === data.name ||
            customType !== undefined
        );
    }

    keyword = ""; // 亲友姓名
    detailData?: Array<Patient> = [];
    loading = false;
    loadError: any;

    clone(): State {
        return Object.assign(new State(), this);
    }
}

export class PatientSourceTypeSelectDialogBloc extends Bloc<_Event, State> {
    static Context = React.createContext<PatientSourceTypeSelectDialogBloc | undefined>(undefined);

    static fromContext(context: PatientSourceTypeSelectDialogBloc): PatientSourceTypeSelectDialogBloc {
        return context;
    }

    constructor(source?: PatientsSourceTypesItem) {
        super();
        this.dispatch(new _EventInit(source)).then();
    }

    private _loadPrimaryTypeTrigger = new Subject<number>();
    private _loadSubTypeTrigger = new Subject<number>();
    private _loadFriendlyRecommendTrigger = new Subject<number>();

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventSelectPrimaryType, this._mapEventSelectPrimaryType);
        map.set(_EventSelectSubtype, this._mapEventSelectSubtype);
        map.set(_EventSearchCustomerName, this._mapEventSearchCustomerName); // 搜索顾客推荐
        map.set(_EventSearchDoctorName, this._mapEventSearchDoctorName); // 搜索医生、导师推荐

        return map;
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    private async *_mapEventInit(event: _EventInit): AsyncGenerator<State> {
        this.innerState.currentSource = JsonMapper.deserialize(PatientSource, event.source ?? {});
        this.innerState.clinicFieldConfig = await OnlinePropertyConfigProvider.instance.getClinicMedicalRecordConfig().catchIgnore();
        this.addDisposable(this._loadPrimaryTypeTrigger);
        this._loadPrimaryTypeTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.startLoading();
                    this.update();
                    return ClinicAgent.getPatientRelativeAdvise().catch((error) => new ABCError(error));
                })
            )
            .subscribe((rsp) => {
                if (rsp instanceof ABCError) {
                    this.innerState.setLoadingError(rsp);
                    this.update();
                    return;
                }
                //调整列表顺序，将医生推荐和导医推荐提到前面
                const referralDoctorType = rsp.rows?.find((item) => item.relatedType == 1 && item.name == SourceType.REFERRAL_DOCTOR); // 转诊医生
                const doctorAdviseType = rsp.rows?.find((item) => item.name == SourceType.DOCTOR_ADVISE); // 医生推荐
                const doctorGuideAdviseType = rsp.rows?.find((item) => item.name == SourceType.DOCTOR_GUIDE_ADVISE); // 导医推荐
                const friendGuideAdviseType = rsp.rows?.find((item) => item.name == SourceType.CUSTOMER_ADVISE); // 顾客推荐
                const customSubType = rsp.rows?.filter((item) => item.children?.length); // 自定义类型 （有子选项）
                const customSourceType = rsp.rows?.filter(
                    (item) =>
                        item.name !== SourceType.REFERRAL_DOCTOR &&
                        item.name !== SourceType.DOCTOR_ADVISE &&
                        item.name !== SourceType.DOCTOR_GUIDE_ADVISE &&
                        item.name !== SourceType.CUSTOMER_ADVISE &&
                        _.isEmpty(item.children)
                ); // 自定义类型 （无子选项）
                const shouldInsertFirst: PatientsSourceTypesItem[] = [];
                if (
                    !this.innerState.clinicFieldConfig?.getFieldConfigDetail({ sourceKey: "patient", type: "create", field: "sourceInfo" })
                        ?.required
                ) {
                    shouldInsertFirst.push({ id: undefined, name: "不指定" });
                }
                referralDoctorType && shouldInsertFirst.push(referralDoctorType);
                doctorAdviseType && shouldInsertFirst.push(doctorAdviseType);
                doctorGuideAdviseType && shouldInsertFirst.push(doctorGuideAdviseType);
                friendGuideAdviseType && shouldInsertFirst.push(friendGuideAdviseType);

                this.innerState.sourceTypes = this.innerState.sourceTypes.concat(
                    ...shouldInsertFirst,
                    customSubType ?? [],
                    customSourceType ?? []
                );

                this.innerState.stopLoading();
                // 编辑患者信息 选择了来源
                if (this.innerState.currentSource?.name) this._loadSubTypeTrigger.next(0);

                this.update();
            })
            .addToDisposableBag(this);

        this._loadPrimaryTypeTrigger.next(0);

        this.addDisposable(this._loadSubTypeTrigger);
        this._loadSubTypeTrigger
            .pipe(
                switchMap(() => {
                    this.innerState.subTypes = [];
                    this.update();
                    const currentPrimaryType = this.innerState.currentSource?.name;
                    if (currentPrimaryType === SourceType.REFERRAL_DOCTOR) {
                        return ClinicAgent.getCurrentDoctors(true)
                            .then((rsp) => {
                                return rsp.map((item) => {
                                    return { id: item.id!, name: item.name!, namePyFirst: item.namePyFirst!, namePy: item.namePy! };
                                });
                            })
                            .catch((error) => new ABCError(error));
                    } else if (currentPrimaryType === SourceType.DOCTOR_ADVISE) {
                        return ClinicAgent.getCurrentDoctors(true)
                            .then((rsp) => {
                                return rsp.map((item) => {
                                    return { id: item.id!, name: item.name!, namePyFirst: item.namePyFirst!, namePy: item.namePy! };
                                });
                            })
                            .catch((error) => new ABCError(error));
                    } else if (currentPrimaryType === SourceType.DOCTOR_GUIDE_ADVISE) {
                        return ClinicAgent.getChainEmployeesSimpleInfo(true)
                            .then((rsp) => {
                                return rsp.map((item) => {
                                    return { id: item.id!, name: item.name!, namePyFirst: item.namePyFirst!, namePy: item.namePy! };
                                });
                            })
                            .catch((error) => new ABCError(error));
                    } else if (currentPrimaryType === SourceType.CUSTOMER_ADVISE) {
                        return ClinicAgent.getPatientRelativeAdvise()
                            .then((rsp) => {
                                return rsp.rows?.map((item) => {
                                    return { id: item.id!, name: item.name! };
                                });
                            })
                            .catch((error) => new ABCError(error));
                    } else if (this.innerState.sourceTypes.length) {
                        return ClinicAgent.getPatientRelativeAdvise()
                            .then((rsp) => {
                                return rsp.rows?.find((sourceTypes) => sourceTypes.id == this.innerState.currentSource?.id);
                            })
                            .then((rsp) => {
                                return rsp?.children ?? [];
                            })
                            .catch((error) => new ABCError(error));
                    }
                    return of<SourceSubType[]>([]);
                })
            )
            .subscribe((rsp) => {
                if (rsp instanceof ABCError) {
                    this.update();
                    return;
                }

                this.innerState.subTypes = rsp ?? [];
                this.update();
            })
            .addToDisposableBag(this);

        this._loadFriendlyRecommendTrigger
            .pipe(
                debounce((/*ignored*/) => {
                    if (_.isEmpty(this.innerState.keyword)) return of(0);
                    return delayed(200);
                }),
                switchMap((/*_*/) => {
                    // this.innerState.loading = true;
                    this.innerState.loading = false;

                    this.innerState.loadError = null;
                    this.update();

                    return PatientAgent.queryPatients(this.innerState.keyword)
                        .catch((error) => new ABCError(error))
                        .toObservable();
                })
            )
            .subscribe((data) => {
                this.innerState.loading = false;

                if (data instanceof ABCError) {
                    this.innerState.loading = false;
                    this.innerState.loadError = data;
                    this.update();
                    return;
                }
                this.innerState.detailData = data?.list;
                this.update();
            });

        this.addDisposable(this._loadFriendlyRecommendTrigger);

        this._loadFriendlyRecommendTrigger.next(0);
        yield this.innerState.clone();
    }

    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }
    // 选择主类型
    private async *_mapEventSelectPrimaryType(event: _EventSelectPrimaryType): AsyncGenerator<State> {
        this.innerState.currentSource!.id = event.data.id;
        this.innerState.currentSource!.name = event.data.name;
        this.innerState.currentSource!.sourceFrom = undefined;
        this.innerState.currentSource!.sourceFromName = undefined;
        this.innerState.keyword = "";
        if (this.innerState.hasSubType(event.data)) {
            this._loadSubTypeTrigger.next(0);
            yield this.innerState.clone();
            return;
        }

        ABCNavigator.pop(this.innerState.currentSource);
    }
    // 选择子类型
    private async *_mapEventSelectSubtype(event: _EventSelectSubtype): AsyncGenerator<State> {
        this.innerState.currentSource!.relatedType = event.data.relatedType;
        this.innerState.currentSource!.sourceFrom = event.data.id;
        this.innerState.currentSource!.sourceFromName = event.data.name;
        ABCNavigator.pop(this.innerState.currentSource);
    }

    // 搜索顾客推荐姓名
    private async *_mapEventSearchCustomerName(event: _EventSearchCustomerName): AsyncGenerator<State> {
        this.innerState.keyword = event.keyword;
        this._loadFriendlyRecommendTrigger.next(0);
    }

    // 搜索医生、导师推荐
    private async *_mapEventSearchDoctorName(event: _EventSearchDoctorName): AsyncGenerator<State> {
        this.innerState.keyword = event.keyword;
        this._collectMatchData();
        yield this.innerState.clone();
    }

    //过滤出符合条件的项
    private _collectMatchData() {
        const keyword = (this.innerState.keyword ?? "").toLowerCase();
        if (keyword.length === 0) {
            this.innerState.matchingPersonnel = this.innerState.subTypes;
        }
        this.innerState.matchingPersonnel = this.innerState.subTypes?.filter(
            (item) =>
                (!!item.name && item.name.toLowerCase().indexOf(keyword) >= 0) ||
                (!!item.namePyFirst && item.namePyFirst.toLowerCase().indexOf(keyword) >= 0) ||
                (!!item.namePy && item.namePy.toLowerCase().indexOf(keyword) >= 0)
        );
    }

    // 顾客推荐搜索
    requestSearchCustomerName(newValue: string): void {
        this.dispatch(new _EventSearchCustomerName(newValue));
    }

    // 医生、导师推荐搜索
    requestSearchDoctorName(keyword: string): void {
        this.dispatch(new _EventSearchDoctorName(keyword));
    }

    requestSelectPrimaryType(data: PatientsSourceTypesItem): void {
        this.dispatch(new _EventSelectPrimaryType(data));
    }

    requestSelectSubType(data: PatientsSourceTypesItem): void {
        this.dispatch(new _EventSelectSubtype(data));
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {
    source?: PatientsSourceTypesItem;

    constructor(source?: PatientsSourceTypesItem) {
        super();
        this.source = source;
    }
}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventSelectPrimaryType extends _Event {
    data: PatientsSourceTypesItem;

    constructor(data: PatientsSourceTypesItem) {
        super();
        this.data = data;
    }
}

class _EventSelectSubtype extends _Event {
    data: PatientsSourceTypesItem;

    constructor(data: PatientsSourceTypesItem) {
        super();
        this.data = data;
    }
}

class _EventSearchCustomerName extends _Event {
    keyword: string;

    constructor(keyword: string) {
        super();
        this.keyword = keyword;
    }
}
class _EventSearchDoctorName extends _Event {
    keyword: string;

    constructor(keyword: string) {
        super();
        this.keyword = keyword;
    }
}
