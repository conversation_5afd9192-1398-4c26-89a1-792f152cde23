import React, { useState } from "react";
import { Dimensions, Text, View, StyleSheet, ScrollView } from "@hippy/react";
import { BasePage } from "./base-ui";
import { LogUtils } from "./common-base-module/log";
import { Colors, Sizes, TextStyles } from "./theme";
import abcOverlay, { OverlayViewKey } from "./base-ui/views/abc-overlay";
import { BaseComponent } from "./base-ui/base-component";
import { sharedPreferences } from "./base-business/preferences/shared-preferences";
import { AbcIconfont } from "@app/abc-mobile-ui";
import { ABCNavigator } from "./base-ui/views/abc-navigator";
import DemoURLProtocols from "./base-ui/demo-url-dispatcher";

const AppUIOverlayViewLocal = "appUIOverlayViewLocal";

interface AppUIOverlayViewProps {}

interface AppUIOverlayViewState {
    position: { top: number; left: number };
}

const cardData = [
    {
        title: "数据展示",
        children: [
            {
                label: "AbcContentEmpty (空状态)",
                value: DemoURLProtocols.AbcContentEmpty,
            },
            {
                label: "AbcAssetImage (引用本地资源图片)",
                value: DemoURLProtocols.AbcAssetImage,
            },
            {
                label: "AbcBadge (展示徽标数字或小红点)",
                value: DemoURLProtocols.AbcBadge,
            },
            {
                label: "AbcTable (表格)",
                value: DemoURLProtocols.AbcTable,
            },
            {
                label: "AbcSvg (svg图标)",
                value: DemoURLProtocols.AbcSvg,
            },
            {
                label: "AbcTag (图标)",
                value: DemoURLProtocols.AbcTag,
            },
            {
                label: "AbcCountDown (倒计时)",
                value: DemoURLProtocols.AbcCountDown,
            },
            {
                label: "AbcList (列表)",
                value: DemoURLProtocols.AbcList,
            },
            {
                label: "AbcText (文字)",
                value: DemoURLProtocols.AbcText,
            },
        ],
    },
    {
        title: "数据录入",
        children: [
            {
                label: "AbcRadio、AbcRadioButtonGroup (单选按钮、单选组合)",
                value: DemoURLProtocols.AbcRadio,
            },
            {
                label: "AbcStepper (步进器)",
                value: DemoURLProtocols.AbcStepper,
            },
            {
                label: "AbcRadioButton、AbcRadioButtonGroup2(单选)",
                value: DemoURLProtocols.AbcRadioButton,
            },
            {
                label: "AbcCheckbox (复选框)",
                value: DemoURLProtocols.AbcCheckbox,
            },
            {
                label: "AbcSwitch (开关)",
                value: DemoURLProtocols.AbcSwitch,
            },
            {
                label: "AbcTextInput (文本输入框)",
                value: DemoURLProtocols.AbcTextInput,
            },
            {
                label: "AbcSearchBar (搜索)",
                value: DemoURLProtocols.AbcSearchBar,
            },
            {
                label: "AbcAddressSelector (地址选择器)",
                value: DemoURLProtocols.AbcAddressSelector,
            },
        ],
    },
    {
        title: "导航组件",
        children: [
            {
                label: "AbcNavigator (导航容器)",
                value: DemoURLProtocols.AbcNavigator,
            },
            {
                label: "AbcTabs (标签页)",
                value: DemoURLProtocols.AbcTabs,
            },
            {
                label: "AbcNavBar (导航栏)",
                value: DemoURLProtocols.AbcNavBar,
            },
        ],
    },
    {
        title: "通用组件",
        children: [
            {
                label: "AbcIconfont (图标)",
                value: DemoURLProtocols.AbcIconfont,
            },
            {
                label: "AbcCalendar (日历单选)",
                value: DemoURLProtocols.AbcCalendar,
            },
            {
                label: "AbcCalendar (日历范围)",
                value: DemoURLProtocols.AbcCalendarRange,
            },
            {
                label: "AbcDatePickerBar (日期选择栏)",
                value: DemoURLProtocols.AbcDatePickerBar,
            },
            {
                label: "AbcButton (按钮)",
                value: DemoURLProtocols.AbcButton,
            },
            {
                label: "AbcBottomNavigationBar (底部导航栏)",
                value: DemoURLProtocols.AbcBottomNavigationBar,
            },
            {
                label: "AbcToast (Toast)",
                value: DemoURLProtocols.AbcToast,
            },
            {
                label: "AbcPopUp (弹出层)",
                value: DemoURLProtocols.AbcPopUp,
            },
        ],
    },
    {
        title: "布局控制",
        children: [
            {
                label: "Flex (flex布局)",
                value: DemoURLProtocols.AbcFlex,
            },
            {
                label: "Grid (grid布局)",
                value: DemoURLProtocols.AbcGrid,
            },
            {
                label: "Divider (分割线)",
                value: DemoURLProtocols.AbcDivider,
            },
        ],
    },
    {
        title: "交互反馈",
        children: [
            {
                label: "Collapse (折叠面板)",
                value: DemoURLProtocols.AbcCollapse,
            },
            {
                label: "Loading (加载)",
                value: DemoURLProtocols.AbcLoading,
            },
            {
                label: "Overlay (覆盖层)",
                value: DemoURLProtocols.AbcOverlay,
            },
            {
                label: "AbcOverlayDialog (覆盖层)",
                value: DemoURLProtocols.AbcOverlayDialog,
            },
            {
                label: "AbcBannerTips (提示通知栏)",
                value: DemoURLProtocols.AbcBannerTips,
            },
            {
                label: "AbcDialog (弹出框)",
                value: DemoURLProtocols.AbcDialog,
            },
            {
                label: "AbcDropdownMenu (下拉菜单)",
                value: DemoURLProtocols.AbcDropdownMenu,
            },
            {
                label: "AbcActionSheet (动作面板)",
                value: DemoURLProtocols.AbcActionSheet,
            },
        ],
    },
    {
        title: "AbcChart",
        children: [
            {
                label: "AbcLineChart",
                value: DemoURLProtocols.AbcLineChart,
            },
            {
                label: "AbcPieChart",
                value: DemoURLProtocols.AbcPieChart,
            },
        ],
    },
    {
        title: "其它",
        children: [
            {
                label: "AbcQrCode (二维码)",
                value: DemoURLProtocols.AbcQrCode,
            },
        ],
    },
];

export function AccordionList(): JSX.Element {
    const [openIndex, setOpenIndex] = useState<number | null>(null);

    function handleClickCardItem(item: { label: string; value: string }) {
        ABCNavigator.navigateToPage(item.value).then();
    }

    return (
        <View>
            {cardData.map((card, idx) => (
                <View key={card.title} style={styles.cardContainer}>
                    <View onClick={() => setOpenIndex(openIndex === idx ? null : idx)} style={styles.cardHeader}>
                        <Text style={styles.cardTitle}>{card.title}</Text>
                        <Text style={styles.arrowIcon}>{openIndex === idx ? "▾" : "▸"}</Text>
                    </View>
                    {openIndex === idx && card.children.length > 0 && (
                        <View style={styles.cardContent}>
                            {card.children.map((child) => (
                                <View key={child.value} style={styles.cardItem} onClick={() => handleClickCardItem(child)}>
                                    <Text style={styles.cardItemText}>{child.label}</Text>
                                    <AbcIconfont name={"Arrow_Right"} size={16} color={Colors.P1} />
                                </View>
                            ))}
                        </View>
                    )}
                </View>
            ))}
        </View>
    );
}

const styles = StyleSheet.create({
    cardContainer: {
        marginBottom: 16,
        borderWidth: 1,
        borderColor: "#e2e2e2",
        borderRadius: 12,
        backgroundColor: "#fff",
        overflow: "hidden",
        shadowColor: "#ebedf0",
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.08,
        shadowRadius: 12,
        elevation: 2,
        minHeight: 60,
    },
    cardHeader: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        height: 60,
        paddingHorizontal: 24,
        backgroundColor: "#fff",
        shadowColor: "#ebedf0",
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 1,
        shadowRadius: 5,
        elevation: 2,
    },
    cardTitle: {
        fontWeight: "600",
        fontSize: 20,
        color: "#222",
    },
    arrowIcon: {
        fontSize: 30,
        color: "#999",
        marginLeft: 8,
    },
    cardContent: {
        backgroundColor: "#fff",
        paddingHorizontal: 18,
        paddingBottom: 8,
    },
    cardItem: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: "#e2e2e2",
        paddingVertical: 12,
    },
    cardItemText: {
        fontSize: 16,
        color: "#333",
    },
});

export class AppUIOverlayView extends BaseComponent<AppUIOverlayViewProps, AppUIOverlayViewState> {
    touchPointPosition: { top: number; right: number } = { top: 0, right: 0 };

    constructor(props: AppUIOverlayViewProps) {
        super(props);
        const _position = sharedPreferences.getObject(AppUIOverlayViewLocal);
        this.state = {
            position: _position
                ? JSON.parse(_position)
                : {
                      top: Sizes.dp30,
                      left: Dimensions.get("window").width - Sizes.dp70,
                  },
        };
    }

    render(): JSX.Element {
        const { position } = this.state;
        return (
            <View
                style={{
                    position: "absolute",
                    top: position.top,
                    left: position.left,
                    height: Sizes.dp40,
                    width: Sizes.dp40,
                    backgroundColor: "#999",
                    borderRadius: Sizes.dp30,
                }}
                onClick={() => {
                    const { URLProtocols } = require("./url-dispatcher");
                    abcOverlay.hide(OverlayViewKey.uiComponentsPage);
                    ABCNavigator.navigateToPage(URLProtocols.ABC_UI_COMPONENTS).then(() => {
                        AppUIPage.show();
                    });
                }}
                onTouchDown={(e) => {
                    this.touchPointPosition = {
                        top: e.page_y - position.top,
                        right: e.page_x - position.left,
                    };
                }}
                onTouchEnd={(e) => {
                    position.top = e.page_y - this.touchPointPosition.top;
                    position.left = e.page_x - this.touchPointPosition.right;
                    sharedPreferences.setObject(AppUIOverlayViewLocal, JSON.stringify(position));
                    this.setState({ position });
                }}
                onTouchMove={(e) => {
                    position.top = e.page_y - this.touchPointPosition.top;
                    position.left = e.page_x - this.touchPointPosition.right;
                    this.setState({ position });
                }}
                onTouchCancel={() => {
                    sharedPreferences.setObject(AppUIOverlayViewLocal, JSON.stringify(this.state.position));
                }}
            >
                <Text style={[{ textAlign: "center" }, TextStyles.t14NW.copyWith({ lineHeight: Sizes.dp40 })]}>UI</Text>
            </View>
        );
    }
}
interface AppLogPageProps {}
export class AppUIPage extends BasePage<AppLogPageProps> {
    static show(): void {
        try {
            abcOverlay.show(<AppUIOverlayView />, OverlayViewKey.uiComponentsPage);
        } catch (e) {
            LogUtils.e("init = " + JSON.stringify(e));
        }
    }
    static hide(): void {
        abcOverlay.hide(OverlayViewKey.uiComponentsPage);
    }
    getAppBarTitle(): string {
        return "App UI";
    }
    constructor(props: AppLogPageProps) {
        super(props);
    }

    renderContent(): JSX.Element | undefined {
        return (
            <View style={{ flex: 1, backgroundColor: Colors.white }}>
                <ScrollView style={{ flex: 1, padding: Sizes.dp16 }}>
                    <AccordionList />
                </ScrollView>
            </View>
        );
    }
}
