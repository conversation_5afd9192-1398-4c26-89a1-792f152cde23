import { callNativeWithPromise, HippyEventEmitter } from "@hippy/react";
import _ from "lodash";
import { LogUtils } from "../log";
import { BehaviorSubject, Subject } from "rxjs";
import { NotificationDataManager } from "../../data/notification";
import { DisposableTracker } from "../cleanup/disposable";
import { timeout } from "rxjs/operators";
import { eventCenter, HomePageFirstShowEvent } from "../../base-business/event-center/event-center";
import { userCenter } from "../../user-center";
import { AnyType } from "../common-types";
import { ignore } from "../global";
import { DeviceUtils } from "../../base-ui/utils/device-utils";

/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/8/6
 *
 * @description
 */

const kModuleName = "Push";

export interface ABCPushMessage {
    url?: string;

    //Local Notification
    //  String NotificationId;
    //  String title;
    //  String presentBadge;
    //  String presentSound;
    //  String presentAlert;
    payload?: string;
}

class PushManager extends DisposableTracker {
    private hippyEventEmitterHandler: any;

    resumeNotification = new Subject<ABCPushMessage>();
    resumeWebview = new Subject<ABCPushMessage>();

    launchNotification = new BehaviorSubject<ABCPushMessage | undefined>(undefined);
    private _lastRegisterAppToken?: string;

    init() {
        LogUtils.d("PushManager.init");
        //退出时，注销push token
        userCenter.addListener({
            onWillLogout: (forInvalidateToken) => {
                if (forInvalidateToken) return Promise.resolve();
                return this.unRegisterDevice().toObservable().pipe(timeout(2000)).toPromise().then();
            },
        });

        const hippyEventEmitter = new HippyEventEmitter();
        this.hippyEventEmitterHandler = hippyEventEmitter.addListener("Push", (evt: { methodName: string; args: any }) => {
            LogUtils.d("PushManager.XGPush push evt = " + JSON.stringify(evt));
            this._handleMethodInvoke(evt.methodName, evt.args);
        });

        this.start(true);
        userCenter.sClinicChangeObserver
            .subscribe((data) => {
                if (data) this._registerDevice();
            })
            .addToDisposableBag(this);

        eventCenter
            .subscribe((event) => {
                if (event instanceof HomePageFirstShowEvent) {
                    this._registerDevice();
                }
            })
            .addToDisposableBag(this);
    }

    start(enableDebug?: boolean) {
        callNativeWithPromise(kModuleName, "start", {
            enableDebug: enableDebug ?? false,
        });
    }

    getDeviceToken(): Promise<string> {
        return callNativeWithPromise(kModuleName, "getDeviceToken", {});
    }

    getPushChannelId(): Promise<number> {
        return callNativeWithPromise(kModuleName, "getPushChannelId", {});
    }

    private _handleMethodInvoke(methodName: string, args: AnyType) {
        if (methodName === "onReceiveDeviceToken") {
            this._onReceiveDeviceToken(args as string);
        } else if (methodName === "onReceiveMessage") {
            this._onReceiveMessage(args);
        } else if (methodName === "onReceiveNotification") {
            this._onReceiveNotification(args);
        } else if (methodName === "onResumeNotification") {
            this._onResumeNotification(JSON.parse(args));
        } else if (methodName === "onLaunchNotification") {
            this._onLaunchNotification(JSON.parse(args));
        } else if (methodName === "onResumeWebview") {
            let _args = args;
            if (typeof _args == "string") {
                _args = JSON.parse(args);
            }
            this._onResumeWebview(_args);
        }
    }

    private _onReceiveDeviceToken(token: string) {
        ignore(token);
        this._registerDevice();
    }

    private _onReceiveMessage(message: AnyType) {
        ignore(message);
        return;
    }

    private _onReceiveNotification(notification: AnyType) {
        ignore(notification);
        return;
    }

    private _onResumeNotification(message: ABCPushMessage) {
        this.resumeNotification.next(message);
    }

    private _onLaunchNotification(message: ABCPushMessage) {
        this.launchNotification.next(message);
    }

    private _onResumeWebview(message: ABCPushMessage) {
        this.resumeWebview.next(message);
    }

    private async _registerDevice() {
        LogUtils.d("_registerDevice");
        const isLogin = userCenter.isLogin();
        const token = await this.getDeviceToken();
        let pushChannel: number;
        if (DeviceUtils.isOhos()) {
            pushChannel = 4;
        } else {
            pushChannel = await this.getPushChannelId();
        }

        if (isLogin && !_.isEmpty(token) && this._lastRegisterAppToken !== userCenter.appToken()) {
            this._lastRegisterAppToken = userCenter.appToken();

            await NotificationDataManager.deviceRegister(token, pushChannel);
        }
    }

    async unRegisterDevice() {
        try {
            const token = await this.getDeviceToken();
            if (!_.isEmpty(token)) {
                await NotificationDataManager.deviceUnRegister(token);
            }
        } catch (e) {
            LogUtils.e("unRegisterDevice " + e);
        }
        return true;
    }
}

const pushManager = new PushManager();
export { pushManager };
